{"indexes": [{"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "venueId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "venueId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "venueId", "order": "ASCENDING"}, {"fieldPath": "tableId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "orders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "venueId", "order": "ASCENDING"}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "waiters", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}]}, {"collectionGroup": "waiters", "queryScope": "COLLECTION", "fields": [{"fieldPath": "venueId", "order": "ASCENDING"}, {"fieldPath": "role", "order": "ASCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "venueId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "products", "queryScope": "COLLECTION", "fields": [{"fieldPath": "venueId", "order": "ASCENDING"}, {"fieldPath": "isModifier", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "tables", "queryScope": "COLLECTION", "fields": [{"fieldPath": "venueId", "order": "ASCENDING"}]}], "fieldOverrides": []}