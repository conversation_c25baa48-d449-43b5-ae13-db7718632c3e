rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // TEMPORARY RULES FOR DATA POPULATION
    // These rules allow unauthenticated writes for initial setup
    // IMPORTANT: Deploy the original firestore.rules after running the populate script
    
    // Venues - Allow all operations temporarily
    match /venues/{venueId} {
      allow read, write: if true;
    }

    // Waiters - Allow all operations temporarily
    match /waiters/{waiterId} {
      allow read, write: if true;
    }

    // Tables - Allow all operations temporarily
    match /tables/{tableId} {
      allow read, write: if true;
    }

    // Products - Allow all operations temporarily
    match /products/{productId} {
      allow read, write: if true;
    }

    // Orders - Allow all operations temporarily
    match /orders/{orderId} {
      allow read, write: if true;
    }

    // Order Items (subcollection) - Allow all operations temporarily
    match /orders/{orderId}/order_items/{orderItemId} {
      allow read, write: if true;
    }
  }
}
