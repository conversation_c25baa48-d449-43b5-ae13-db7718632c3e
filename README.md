# Restaurant Order Admin App

A mobile-friendly admin application for restaurant staff to manage table orders in a high-traffic environment. Built with Ionic and Angular.

## Features

- **Dark Mode UI**: Easy on the eyes for restaurant environments
- **Order Management**: Accept or reject incoming orders
- **Order History**: View completed and rejected orders
- **Order Details**: See detailed information about each order
- **Rejection Reasons**: Select from predefined reasons when rejecting an order
- **High-Traffic Optimized**: Designed for busy restaurant environments

## Project Structure

- **Models**: Data models for orders and related entities
- **Services**: Business logic and data handling
- **Pages**: Main application views
  - Orders: View and manage pending orders
  - History: View completed and rejected orders
  - Order Detail: View detailed information about a specific order
- **Components**: Reusable UI components

## Getting Started

### Prerequisites

- Node.js and npm
- Ionic CLI
- Angular CLI

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/order-flow-qr.git
cd order-flow-qr
```

2. Install dependencies
```bash
npm install
```

3. Run the development server
```bash
ionic serve
```

## Development

### Adding New Features

The project is structured to make adding new features straightforward:

1. Create new models in the `src/app/models` directory
2. Add services in the `src/app/services` directory
3. Create new pages using the Ionic CLI:
```bash
ionic generate page pages/your-page-name
```
4. Create new components using the Angular CLI:
```bash
ng generate component components/your-component-name
```

### Building for Production

```bash
ionic build --prod
```

## Future Enhancements

- Real-time order notifications
- Staff management
- Kitchen view for food preparation
- Customer feedback integration
- Analytics dashboard
- Offline support

## License

This project is licensed under the MIT License - see the LICENSE file for details.
