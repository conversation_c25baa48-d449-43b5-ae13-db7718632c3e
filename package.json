{"name": "order-flow-qr", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "populate-firebase": "npx tsx src/app/scripts/populate-firebase.ts"}, "private": true, "dependencies": {"@angular/animations": "^19.2.13", "@angular/common": "^19.2.13", "@angular/compiler": "^19.2.13", "@angular/core": "^19.2.13", "@angular/fire": "^19.2.0", "@angular/forms": "^19.2.13", "@angular/platform-browser": "^19.2.13", "@angular/platform-browser-dynamic": "^19.2.13", "@angular/router": "^19.2.13", "@capacitor/app": "5.0.6", "@capacitor/core": "5.5.1", "@capacitor/haptics": "5.0.6", "@capacitor/keyboard": "5.0.6", "@capacitor/status-bar": "5.0.6", "@ionic/angular": "^8.5.7", "firebase": "^11.8.1", "ionicons": "^7.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.13", "@angular-eslint/builder": "^16.0.0", "@angular-eslint/eslint-plugin": "^16.0.0", "@angular-eslint/eslint-plugin-template": "^16.0.0", "@angular-eslint/template-parser": "^16.0.0", "@angular/cli": "^19.2.13", "@angular/compiler-cli": "^19.2.13", "@angular/language-service": "^19.2.13", "@capacitor/cli": "5.5.1", "@ionic/angular-toolkit": "^9.0.0", "@types/jasmine": "~4.3.0", "@types/node": "^12.11.1", "@typescript-eslint/eslint-plugin": "5.3.0", "@typescript-eslint/parser": "5.3.0", "eslint": "^7.6.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsdoc": "30.7.6", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~4.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "tsx": "^4.7.0", "typescript": "~5.8.3"}, "description": "Restaurant Order Admin App"}