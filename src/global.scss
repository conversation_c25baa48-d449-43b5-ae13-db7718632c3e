/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import '@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/* Custom global styles */
:root {
  --ion-color-primary: #3880ff;
  --ion-color-success: #2dd36f;
  --ion-color-warning: #ffc409;
  --ion-color-danger: #eb445a;
  --ion-color-medium: #92949c;
  --ion-color-light: #f4f5f8;

  /* Custom colors */
  --app-background: #121212;
  --card-background: #1e1e1e;
  --yellow-border: #ffc409;
  --text-light: #ffffff;
  --text-secondary: #a0a0a0;
  --button-reject: #eb445a;
  --button-accept: #2dd36f;
}

/* Global styles */
ion-content {
  --background: var(--app-background);
}

ion-toolbar {
  --background: var(--app-background);
  --color: var(--text-light);
}

ion-item {
  --background: var(--card-background);
  --color: var(--text-light);
}

/* Order card styles */
.order-card {
  margin: 16px 8px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.order-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--yellow-border);
}

.pending-order {
  border-left: 4px solid var(--yellow-border);
}

.completed-order {
  border-left: 4px solid var(--button-accept);
}

.rejected-order {
  border-left: 4px solid var(--button-reject);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.order-time {
  font-size: 0.8rem;
  color: var(--text-secondary);
  text-align: right;
}

.order-id {
  font-size: 1rem;
  font-weight: bold;
  color: var(--yellow-border);
}

.order-table {
  font-size: 1.1rem;
  font-weight: bold;
  margin: 4px 0 8px 0;
}

.order-items {
  margin: 8px 0;
}

.order-items p {
  margin: 4px 0;
  color: var(--text-light);
}

.order-total {
  font-weight: bold;
  margin: 8px 0;
  font-size: 1.05rem;
}

.special-instructions {
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Action buttons */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.action-buttons ion-button {
  margin-left: 8px;
  --border-radius: 20px;
  font-weight: 500;
  text-transform: none;
}

.action-buttons ion-button.reject-btn {
  --background: var(--button-reject);
  --color: white;
}

.action-buttons ion-button.accept-btn {
  --background: var(--button-accept);
  --color: white;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-state ion-icon {
  font-size: 64px;
  color: var(--text-secondary);
}

.empty-state h3 {
  margin-top: 16px;
  color: var(--text-light);
}

.empty-state p {
  color: var(--text-secondary);
  max-width: 300px;
  margin: 8px auto;
}

/* Tab bar styling */
ion-tab-bar {
  --background: var(--app-background);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

ion-tab-button {
  --color: var(--text-secondary);
  --color-selected: var(--text-light);
}

ion-badge {
  --background: var(--button-reject);
  --color: white;
  font-weight: bold;
}
