import { Injectable } from '@angular/core';
import { Firestore, collection, query, where, getDocs, doc, getDoc } from '@angular/fire/firestore';
import { BehaviorSubject, Observable } from 'rxjs';
import { Waiter, Venue } from '../models/order.model';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private currentWaiterSubject = new BehaviorSubject<Waiter | null>(null);
  public currentWaiter$ = this.currentWaiterSubject.asObservable();

  private currentVenueSubject = new BehaviorSubject<Venue | null>(null);
  public currentVenue$ = this.currentVenueSubject.asObservable();

  constructor(
    private firestore: Firestore,
    private authService: AuthService
  ) {
    // Listen to auth changes and load user profile
    this.authService.currentUser$.subscribe(user => {
      if (user?.email) {
        this.loadWaiterProfile(user.email);
      } else {
        this.clearUserData();
      }
    });
  }

  private async loadWaiterProfile(email: string): Promise<void> {
    try {
      // Query waiters collection by email
      const waitersRef = collection(this.firestore, 'waiters');
      const q = query(waitersRef, where('email', '==', email));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const waiterDoc = querySnapshot.docs[0];
        const waiterData = {
          id: waiterDoc.id,
          ...waiterDoc.data(),
          lastLogin: waiterDoc.data()['lastLogin']?.toDate()
        } as Waiter;

        this.currentWaiterSubject.next(waiterData);

        // Load venue data
        if (waiterData.venueId) {
          await this.loadVenueData(waiterData.venueId);
        }
      } else {
        console.warn('No waiter profile found for email:', email);
        this.clearUserData();
      }
    } catch (error) {
      console.error('Error loading waiter profile:', error);
      this.clearUserData();
    }
  }

  private async loadVenueData(venueId: string): Promise<void> {
    try {
      const venueDoc = doc(this.firestore, 'venues', venueId);
      const venueSnapshot = await getDoc(venueDoc);

      if (venueSnapshot.exists()) {
        const venueData = {
          id: venueSnapshot.id,
          ...venueSnapshot.data()
        } as Venue;

        this.currentVenueSubject.next(venueData);
      } else {
        console.warn('No venue found for ID:', venueId);
      }
    } catch (error) {
      console.error('Error loading venue data:', error);
    }
  }

  private clearUserData(): void {
    this.currentWaiterSubject.next(null);
    this.currentVenueSubject.next(null);
  }

  getCurrentWaiter(): Waiter | null {
    return this.currentWaiterSubject.value;
  }

  getCurrentVenue(): Venue | null {
    return this.currentVenueSubject.value;
  }

  getCurrentVenueId(): string | null {
    return this.currentVenueSubject.value?.id || null;
  }

  hasPermission(permission: keyof Waiter['permissions']): boolean {
    const waiter = this.getCurrentWaiter();
    return waiter?.permissions[permission] || false;
  }

  isAdmin(): boolean {
    const waiter = this.getCurrentWaiter();
    return waiter?.role === 'admin';
  }

  isManager(): boolean {
    const waiter = this.getCurrentWaiter();
    return waiter?.role === 'manager' || waiter?.role === 'admin';
  }

  getWaiterDisplayName(): string {
    const waiter = this.getCurrentWaiter();
    if (waiter) {
      return `${waiter.name.first} ${waiter.name.last}`;
    }
    return 'Unknown User';
  }
}
