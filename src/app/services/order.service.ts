import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, tap, filter, switchMap } from 'rxjs/operators';
import { Order, OrderStatus, RejectionReason } from '../models/order.model';
import { environment } from '../../environments/environment';
import { UserService } from './user.service';

// Firestore imports
import { Firestore, collection, collectionData, doc, updateDoc, query, where, orderBy, Timestamp, getDocs } from '@angular/fire/firestore';

@Injectable({
  providedIn: 'root'
})
export class OrderService {
  private ordersSubject = new BehaviorSubject<Order[]>([]);
  public orders$ = this.ordersSubject.asObservable();



  private http = inject(HttpClient);
  private firestore = inject(Firestore);
  private userService = inject(UserService);

  constructor() {
    // Load orders from Firestore when venue is available
    this.userService.currentVenue$.pipe(
      filter(venue => !!venue)
    ).subscribe(venue => {
      if (venue) {
        this.loadOrdersFromFirestore(venue.id);
      }
    });
  }

  private async loadOrdersFromFirestore(venueId: string) {
    const ordersCollection = collection(this.firestore, 'orders');
    // Filter orders by venue and order by creation date
    const ordersQuery = query(
      ordersCollection,
      where('venueId', '==', venueId),
      orderBy('createdAt', 'desc')
    );

    collectionData(ordersQuery, { idField: 'id' }).subscribe(async (firestoreOrders: any[]) => {
      if (firestoreOrders.length > 0) {
        // Convert Firestore data to Order objects and load items for each order
        const orders: Order[] = await Promise.all(
          firestoreOrders.map(async (order) => {
            // Load order items from subcollection
            const orderItems = await this.getOrderItems(order.id);

            // Convert Firestore order items to display format
            const items = orderItems.map((item: any) => ({
              id: item.id,
              name: item.productName || item.name || 'Unknown Item',
              quantity: item.quantity || 1,
              price: item.unitPrice || item.price || 0,
              modifiers: item.modifiers || [],
              notes: item.specialInstructions || item.notes
            }));

            return {
              ...order,
              // Map Firestore totals.total to display total
              total: order.totals?.total || order.total || 0,
              // Use loaded items from subcollection
              items: items,
              createdAt: order.createdAt?.toDate() || new Date(),
              updatedAt: order.updatedAt?.toDate() || new Date()
            };
          })
        );
        this.ordersSubject.next(orders);
      } else {
        // If no Firestore orders, set empty array
        this.ordersSubject.next([]);
      }
    });
  }

  getOrders(): Observable<Order[]> {
    return this.orders$;
  }

  refreshOrders(): void {
    const venueId = this.userService.getCurrentVenueId();
    if (venueId) {
      this.loadOrdersFromFirestore(venueId);
    }
  }

  getOrderById(id: string): Observable<Order | undefined> {
    // In a real app, this would be an HTTP request
    // return this.http.get<Order>(`${environment.apiUrl}/orders/${id}`);

    // Look in current orders (includes both Firestore and mock data)
    return this.orders$.pipe(
      map(orders => orders.find(order => order.id === id))
    );
  }

  getPendingOrders(): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders
        .filter(order => order.status === OrderStatus.PENDING)
        .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
      )
    );
  }

  getCompletedOrders(): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders
        .filter(order => order.status === OrderStatus.CONFIRMED)
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      )
    );
  }

  getRejectedOrders(): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders
        .filter(order => order.status === OrderStatus.REJECTED)
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      )
    );
  }

  async acceptOrder(orderId: string): Promise<Observable<Order>> {
    try {
      // Update in Firestore
      const orderDoc = doc(this.firestore, 'orders', orderId);
      await updateDoc(orderDoc, {
        status: OrderStatus.CONFIRMED,
        updatedAt: Timestamp.now()
      });

      // Update local data
      const updatedOrders = this.ordersSubject.value.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.CONFIRMED,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    } catch (error) {
      console.error('Error accepting order:', error);
      throw error;
    }
  }

  // Method to fetch order items from subcollection
  async getOrderItems(orderId: string): Promise<any[]> {
    try {
      const orderItemsRef = collection(this.firestore, 'orders', orderId, 'order_items');
      const orderItemsSnapshot = await getDocs(orderItemsRef);

      return orderItemsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data()['createdAt']?.toDate() || new Date(),
        updatedAt: doc.data()['updatedAt']?.toDate() || new Date()
      }));
    } catch (error) {
      console.error('Error fetching order items:', error);
      return [];
    }
  }

  async rejectOrder(orderId: string, reason: RejectionReason, note?: string): Promise<Observable<Order>> {
    try {
      // Update in Firestore
      const orderDoc = doc(this.firestore, 'orders', orderId);
      await updateDoc(orderDoc, {
        status: OrderStatus.REJECTED,
        rejectionReason: reason,
        rejectionNote: note,
        updatedAt: Timestamp.now()
      });

      // Update local data
      const updatedOrders = this.ordersSubject.value.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.REJECTED,
            rejectionReason: reason,
            rejectionNote: note,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    } catch (error) {
      console.error('Error rejecting order:', error);
      throw error;
    }
  }
}
