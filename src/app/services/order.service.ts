import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, tap, filter, switchMap } from 'rxjs/operators';
import { Order, OrderStatus, RejectionReason } from '../models/order.model';
import { environment } from '../../environments/environment';
import { UserService } from './user.service';

// Firestore imports
import { Firestore, collection, collectionData, doc, updateDoc, query, where, orderBy, Timestamp, getDocs } from '@angular/fire/firestore';

@Injectable({
  providedIn: 'root'
})
export class OrderService {
  private ordersSubject = new BehaviorSubject<Order[]>([]);
  public orders$ = this.ordersSubject.asObservable();

  // Mock data for demo purposes
  private mockOrders: Order[] = [
    {
      id: '1001',
      venueId: 'venue_001',
      tableId: 'table_001',
      tableName: 'Table 12',
      orderNumber: 'ORD-1001',
      items: [
        {
          id: 'i1',
          name: 'Margherita Pizza',
          quantity: 1,
          price: 12.99,
          modifiers: [
            { id: 'm1', name: 'Extra Cheese', price: 1.50 }
          ]
        },
        {
          id: 'i2',
          name: 'Garlic Bread',
          quantity: 1,
          price: 4.99
        }
      ],
      status: OrderStatus.PENDING,
      total: 19.48, // 12.99 + 1.50 + 4.99
      createdAt: new Date(new Date().getTime() - 5 * 60000), // 5 minutes ago
      updatedAt: new Date(new Date().getTime() - 5 * 60000)
    },
    {
      id: '1002',
      venueId: 'venue_001',
      tableId: 'table_002',
      tableName: 'Table 8',
      orderNumber: 'ORD-1002',
      items: [
        {
          id: 'i3',
          name: 'Chicken Alfredo',
          quantity: 1,
          price: 15.99,
          modifiers: [
            { id: 'm2', name: 'No Croutons', price: 0 },
            { id: 'm3', name: 'Extra Parmesan', price: 2.00 }
          ]
        }
      ],
      status: OrderStatus.PENDING,
      total: 17.99, // 15.99 + 0 + 2.00
      specialInstructions: 'No croutons on salad',
      createdAt: new Date(new Date().getTime() - 12 * 60000), // 12 minutes ago
      updatedAt: new Date(new Date().getTime() - 12 * 60000)
    },
    {
      id: '1003',
      venueId: 'venue_001',
      tableId: 'table_003',
      tableName: 'Table 5',
      orderNumber: 'ORD-1003',
      items: [
        {
          id: 'i4',
          name: 'Caesar Salad',
          quantity: 1,
          price: 8.99
        },
        {
          id: 'i5',
          name: 'Grilled Salmon',
          quantity: 1,
          price: 22.99
        }
      ],
      status: OrderStatus.CONFIRMED,
      total: 31.98,
      createdAt: new Date(new Date().getTime() - 45 * 60000), // 45 minutes ago
      updatedAt: new Date(new Date().getTime() - 30 * 60000)  // 30 minutes ago
    },
    {
      id: '1004',
      venueId: 'venue_001',
      tableId: 'table_004',
      tableName: 'Table 3',
      orderNumber: 'ORD-1004',
      items: [
        {
          id: 'i6',
          name: 'Beef Burger',
          quantity: 2,
          price: 14.99
        }
      ],
      status: OrderStatus.CONFIRMED,
      total: 29.98,
      specialInstructions: 'Medium rare',
      createdAt: new Date(new Date().getTime() - 60 * 60000), // 1 hour ago
      updatedAt: new Date(new Date().getTime() - 45 * 60000)  // 45 minutes ago
    },
    {
      id: '1005',
      venueId: 'venue_001',
      tableId: 'table_005',
      tableName: 'Table 7',
      orderNumber: 'ORD-1005',
      items: [
        {
          id: 'i7',
          name: 'Vegetarian Pizza',
          quantity: 1,
          price: 16.99
        }
      ],
      status: OrderStatus.REJECTED,
      rejectionReason: 'Out of stock',
      rejectionNote: 'No more vegetarian toppings available',
      total: 16.99,
      createdAt: new Date(new Date().getTime() - 90 * 60000), // 1.5 hours ago
      updatedAt: new Date(new Date().getTime() - 85 * 60000)  // 1 hour 25 minutes ago
    },
    {
      id: '1006',
      venueId: 'venue_001',
      tableId: 'table_006',
      tableName: 'Table 2',
      orderNumber: 'ORD-1006',
      items: [
        {
          id: 'i8',
          name: 'Fish and Chips',
          quantity: 1,
          price: 18.99
        }
      ],
      status: OrderStatus.REJECTED,
      rejectionReason: 'Kitchen closed',
      rejectionNote: 'Kitchen equipment malfunction',
      total: 18.99,
      createdAt: new Date(new Date().getTime() - 120 * 60000), // 2 hours ago
      updatedAt: new Date(new Date().getTime() - 115 * 60000)  // 1 hour 55 minutes ago
    }
  ];

  private http = inject(HttpClient);
  private firestore = inject(Firestore);
  private userService = inject(UserService);

  constructor() {
    // Initialize with mock data for demo
    this.ordersSubject.next(this.mockOrders);

    // Load orders from Firestore when venue is available
    this.userService.currentVenue$.pipe(
      filter(venue => !!venue)
    ).subscribe(venue => {
      if (venue) {
        this.loadOrdersFromFirestore(venue.id);
      }
    });
  }

  private async loadOrdersFromFirestore(venueId: string) {
    const ordersCollection = collection(this.firestore, 'orders');
    // Filter orders by venue and order by creation date
    const ordersQuery = query(
      ordersCollection,
      where('venueId', '==', venueId),
      orderBy('createdAt', 'desc')
    );

    collectionData(ordersQuery, { idField: 'id' }).subscribe(async (firestoreOrders: any[]) => {
      if (firestoreOrders.length > 0) {
        // Convert Firestore data to Order objects and load items for each order
        const orders: Order[] = await Promise.all(
          firestoreOrders.map(async (order) => {
            // Load order items from subcollection
            const orderItems = await this.getOrderItems(order.id);

            // Convert Firestore order items to display format
            const items = orderItems.map((item: any) => ({
              id: item.id,
              name: item.productName || item.name || 'Unknown Item',
              quantity: item.quantity || 1,
              price: item.unitPrice || item.price || 0,
              modifiers: item.modifiers || [],
              notes: item.specialInstructions || item.notes
            }));

            return {
              ...order,
              // Map Firestore totals.total to display total
              total: order.totals?.total || order.total || 0,
              // Use loaded items from subcollection
              items: items,
              createdAt: order.createdAt?.toDate() || new Date(),
              updatedAt: order.updatedAt?.toDate() || new Date()
            };
          })
        );
        this.ordersSubject.next(orders);
      } else {
        // If no Firestore orders, use mock data filtered by venue
        const filteredMockOrders = this.mockOrders.filter(order => order.venueId === venueId);
        this.ordersSubject.next(filteredMockOrders);
      }
    });
  }

  getOrders(): Observable<Order[]> {
    return this.orders$;
  }

  refreshOrders(): void {
    const venueId = this.userService.getCurrentVenueId();
    if (venueId) {
      this.loadOrdersFromFirestore(venueId);
    }
  }

  getOrderById(id: string): Observable<Order | undefined> {
    // In a real app, this would be an HTTP request
    // return this.http.get<Order>(`${environment.apiUrl}/orders/${id}`);

    // Look in current orders (includes both Firestore and mock data)
    return this.orders$.pipe(
      map(orders => orders.find(order => order.id === id))
    );
  }

  getPendingOrders(): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders
        .filter(order => order.status === OrderStatus.PENDING)
        .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
      )
    );
  }

  getCompletedOrders(): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders
        .filter(order => order.status === OrderStatus.CONFIRMED)
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      )
    );
  }

  getRejectedOrders(): Observable<Order[]> {
    return this.orders$.pipe(
      map(orders => orders
        .filter(order => order.status === OrderStatus.REJECTED)
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      )
    );
  }

  async acceptOrder(orderId: string): Promise<Observable<Order>> {
    try {
      // Update in Firestore
      const orderDoc = doc(this.firestore, 'orders', orderId);
      await updateDoc(orderDoc, {
        status: OrderStatus.CONFIRMED,
        updatedAt: Timestamp.now()
      });

      // Update local data
      const updatedOrders = this.ordersSubject.value.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.CONFIRMED,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    } catch (error) {
      console.error('Error accepting order:', error);

      // Fallback to mock data
      const updatedOrders = this.mockOrders.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.CONFIRMED,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.mockOrders = updatedOrders;
      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    }
  }

  // Method to fetch order items from subcollection
  async getOrderItems(orderId: string): Promise<any[]> {
    try {
      const orderItemsRef = collection(this.firestore, 'orders', orderId, 'order_items');
      const orderItemsSnapshot = await getDocs(orderItemsRef);

      return orderItemsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data()['createdAt']?.toDate() || new Date(),
        updatedAt: doc.data()['updatedAt']?.toDate() || new Date()
      }));
    } catch (error) {
      console.error('Error fetching order items:', error);
      return [];
    }
  }

  async rejectOrder(orderId: string, reason: RejectionReason, note?: string): Promise<Observable<Order>> {
    try {
      // Update in Firestore
      const orderDoc = doc(this.firestore, 'orders', orderId);
      await updateDoc(orderDoc, {
        status: OrderStatus.REJECTED,
        rejectionReason: reason,
        rejectionNote: note,
        updatedAt: Timestamp.now()
      });

      // Update local data
      const updatedOrders = this.ordersSubject.value.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.REJECTED,
            rejectionReason: reason,
            rejectionNote: note,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    } catch (error) {
      console.error('Error rejecting order:', error);

      // Fallback to mock data
      const updatedOrders = this.mockOrders.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            status: OrderStatus.REJECTED,
            rejectionReason: reason,
            rejectionNote: note,
            updatedAt: new Date()
          };
        }
        return order;
      });

      this.mockOrders = updatedOrders;
      this.ordersSubject.next(updatedOrders);
      return of(updatedOrders.find(order => order.id === orderId) as Order);
    }
  }
}
