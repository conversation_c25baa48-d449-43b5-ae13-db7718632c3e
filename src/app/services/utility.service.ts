import { Injectable } from '@angular/core';
import { OrderStatus } from '../models/order.model';

@Injectable({
  providedIn: 'root'
})
export class UtilityService {

  constructor() { }

  /**
   * Format a date to a relative time string (e.g., "5 minutes ago")
   */
  getTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - new Date(date).getTime();
    const diffMins = Math.round(diffMs / 60000);

    if (diffMins < 1) {
      return 'Just now';
    } else if (diffMins === 1) {
      return '1 minute ago';
    } else if (diffMins < 60) {
      return `${diffMins} minutes ago`;
    } else if (diffMins < 120) {
      return '1 hour ago';
    } else if (diffMins < 1440) {
      return `${Math.floor(diffMins / 60)} hours ago`;
    } else if (diffMins < 2880) {
      return '1 day ago';
    } else {
      return `${Math.floor(diffMins / 1440)} days ago`;
    }
  }

  /**
   * Format a date to a localized date and time string
   */
  formatDateTime(date: Date): string {
    return new Date(date).toLocaleString();
  }

  /**
   * Format a date to a localized time string
   */
  formatTime(date: Date): string {
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  /**
   * Get the appropriate color for an order status
   */
  getStatusColor(status: OrderStatus): string {
    switch (status) {
      case OrderStatus.PENDING:
        return 'warning';
      case OrderStatus.CONFIRMED:
        return 'success';
      case OrderStatus.REJECTED:
        return 'danger';
      default:
        return 'medium';
    }
  }

  /**
   * Format a currency value
   */
  formatCurrency(value: number | undefined): string {
    if (value === undefined || value === null || isNaN(value)) {
      return '0.00 RON';
    }
    return `${value.toFixed(2)} RON`;
  }
}
