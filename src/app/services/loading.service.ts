import { Injectable } from '@angular/core';
import { LoadingController } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  private loading: HTMLIonLoadingElement | null = null;

  constructor(private loadingController: LoadingController) { }

  async present(message: string = 'Please wait...') {
    // Dismiss any existing loading
    await this.dismiss();

    // Create and present new loading
    this.loading = await this.loadingController.create({
      message,
      spinner: 'circles',
      cssClass: 'custom-loading'
    });

    return await this.loading.present();
  }

  async dismiss() {
    if (this.loading) {
      await this.loading.dismiss();
      this.loading = null;
    }
  }
}
