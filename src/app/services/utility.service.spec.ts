import { TestBed } from '@angular/core/testing';
import { UtilityService } from './utility.service';
import { OrderStatus } from '../models/order.model';

describe('UtilityService', () => {
  let service: UtilityService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(UtilityService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getTimeAgo', () => {
    it('should return "Just now" for times less than 1 minute ago', () => {
      const date = new Date(new Date().getTime() - 30 * 1000); // 30 seconds ago
      expect(service.getTimeAgo(date)).toBe('Just now');
    });

    it('should return "1 minute ago" for times 1 minute ago', () => {
      const date = new Date(new Date().getTime() - 60 * 1000); // 1 minute ago
      expect(service.getTimeAgo(date)).toBe('1 minute ago');
    });

    it('should return "X minutes ago" for times less than 1 hour ago', () => {
      const date = new Date(new Date().getTime() - 30 * 60 * 1000); // 30 minutes ago
      expect(service.getTimeAgo(date)).toBe('30 minutes ago');
    });

    it('should return "1 hour ago" for times 1 hour ago', () => {
      const date = new Date(new Date().getTime() - 60 * 60 * 1000); // 1 hour ago
      expect(service.getTimeAgo(date)).toBe('1 hour ago');
    });

    it('should return "X hours ago" for times less than 1 day ago', () => {
      const date = new Date(new Date().getTime() - 5 * 60 * 60 * 1000); // 5 hours ago
      expect(service.getTimeAgo(date)).toBe('5 hours ago');
    });

    it('should return "1 day ago" for times 1 day ago', () => {
      const date = new Date(new Date().getTime() - 24 * 60 * 60 * 1000); // 1 day ago
      expect(service.getTimeAgo(date)).toBe('1 day ago');
    });

    it('should return "X days ago" for times more than 1 day ago', () => {
      const date = new Date(new Date().getTime() - 3 * 24 * 60 * 60 * 1000); // 3 days ago
      expect(service.getTimeAgo(date)).toBe('3 days ago');
    });
  });

  describe('formatDateTime', () => {
    it('should format a date to a localized date and time string', () => {
      const date = new Date(2023, 0, 1, 12, 30, 0); // Jan 1, 2023, 12:30:00
      const result = service.formatDateTime(date);
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
    });
  });

  describe('formatTime', () => {
    it('should format a date to a localized time string', () => {
      const date = new Date(2023, 0, 1, 12, 30, 0); // Jan 1, 2023, 12:30:00
      const result = service.formatTime(date);
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
    });
  });

  describe('getStatusColor', () => {
    it('should return "warning" for pending status', () => {
      expect(service.getStatusColor(OrderStatus.PENDING)).toBe('warning');
    });

    it('should return "success" for completed status', () => {
      expect(service.getStatusColor(OrderStatus.COMPLETED)).toBe('success');
    });

    it('should return "danger" for rejected status', () => {
      expect(service.getStatusColor(OrderStatus.REJECTED)).toBe('danger');
    });

    it('should return "medium" for unknown status', () => {
      expect(service.getStatusColor('unknown' as OrderStatus)).toBe('medium');
    });
  });

  describe('formatCurrency', () => {
    it('should format a number as currency with $ symbol and 2 decimal places', () => {
      expect(service.formatCurrency(10)).toBe('$10.00');
      expect(service.formatCurrency(10.5)).toBe('$10.50');
      expect(service.formatCurrency(10.55)).toBe('$10.55');
      expect(service.formatCurrency(10.555)).toBe('$10.56'); // Rounds up
      expect(service.formatCurrency(0)).toBe('$0.00');
    });
  });
});
