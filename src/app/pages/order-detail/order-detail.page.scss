.order-detail-container {
  padding: 0 6px;
  margin-top: 8px;
}

.order-header-section {
  background-color: var(--card-background);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.status-badge {
  margin-bottom: 8px;
}

ion-badge {
  text-transform: capitalize;
  padding: 6px 10px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.8rem;
}

.pending {
  background-color: var(--yellow-border);
  color: black;
}

.accepted {
  background-color: var(--button-accept);
  color: black;
}

.completed {
  background-color: var(--button-accept);
  color: black;
}

.rejected {
  background-color: var(--button-reject);
  color: white;
}

.order-id {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--text-light);
  margin-bottom: 16px;
}

.order-info {
  margin-bottom: 16px;
}

.info-item {
  margin-bottom: 12px;
}

.info-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 4px;
}

.info-value {
  color: var(--text-light);
  font-size: 1rem;
}

.rejection-info, .special-instructions {
  background-color: rgba(255, 255, 255, 0.05);
  padding: 10px;
  border-radius: 4px;
  margin-top: 12px;
}

.order-items-section {
  background-color: var(--card-background);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.section-title {
  font-size: 1.1rem;
  font-weight: bold;
  color: var(--text-light);
  margin-bottom: 12px;
}

.order-items {
  margin: 8px 0;
}

.item-row {
  display: flex;
  margin-bottom: 6px;
  align-items: flex-start;
}

.item-quantity {
  width: 30px;
  font-weight: 500;
  color: var(--text-light);
  margin-right: 8px;
  padding-left: 6px;
}

.item-details {
  flex: 1;
}

.item-name {
  font-size: 14px;
  color: var(--text-light);
  margin-bottom: 2px;
}

.modifier-row {
  margin-bottom: 2px;
}

.modifier-name {
  font-size: 12px;
  color: var(--text-secondary);
}

.modifier-price {
  font-size: 12px;
}

.item-notes {
  font-size: 12px;
  color: var(--text-secondary);
  margin-left: 4px;
  margin-top: 4px;
  font-style: italic;
}

.item-price {
  font-size: 14px;
  color: var(--text-secondary);
  text-align: right;
  min-width: 90px;
}

.order-total {
  font-weight: bold;
  margin: 16px 0 8px;
  color: var(--text-light);
  text-align: right;
  font-size: 16px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  gap: 12px;
}

.action-buttons ion-button {
  height: 40px;
  font-size: 0.9rem;
  --border-radius: 8px;
}

.reject-btn {
  --background: var(--button-reject);
  --color: white;
  min-width: 90px;
}

.accept-btn {
  --background: var(--button-accept);
  --color: black;
  min-width: 90px;
}
