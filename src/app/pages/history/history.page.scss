ion-segment {
  padding: 0 16px;
  --background: var(--app-background);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 10;
}

ion-segment-button {
  --color: var(--text-secondary);
  --color-checked: var(--text-light);
  --indicator-color: var(--button-accept);
}

.orders-container {
  padding: 0 6px;
  margin-top: 8px;
}

.order-card {
  position: relative;
  background-color: var(--card-background);
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  display: flex;
  align-items: stretch;
}

.completed-order {
  position: relative;
}

.completed-order::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--button-accept);
}

.rejected-order {
  position: relative;
}

.rejected-order::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--button-reject);
}

.order-content {
  flex: 1;
  padding: 12px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  gap: 12px;
}

.action-buttons ion-button {
  height: 40px;
  font-size: 0.9rem;
  --border-radius: 8px;
}

.details-btn {
  --color: var(--text-light);
  --border-color: var(--text-secondary);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.order-table {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--text-light);
}

.order-time {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.order-id {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 12px;
}

.order-items {
  margin: 8px 0;
}

.item-row {
  display: flex;
  margin-bottom: 6px;
  align-items: flex-start;
}

.item-quantity {
  width: 30px;
  font-weight: 500;
  color: var(--text-light);
  margin-right: 8px;
  padding-left: 6px;
}

.item-details {
  flex: 1;
}

.item-name {
  font-size: 14px;
  color: var(--text-light);
  margin-bottom: 2px;
}

.modifier-row {
  margin-bottom: 2px;
}

.modifier-name {
  font-size: 12px;
  color: var(--text-secondary);
}

.modifier-price {
  font-size: 12px;
}

.item-price {
  font-size: 14px;
  color: var(--text-secondary);
  text-align: right;
  min-width: 90px;
}

.order-total {
  font-weight: bold;
  margin: 16px 0 8px;
  color: var(--text-light);
  text-align: right;
  font-size: 16px;
}

.rejection-reason {
  margin-top: 12px;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  font-size: 0.9rem;
  color: var(--button-reject);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  color: var(--text-secondary);
  text-align: center;
  padding: 20px;
}

.empty-state ion-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-light);
}

.empty-state p {
  margin: 0;
  font-size: 16px;
}
