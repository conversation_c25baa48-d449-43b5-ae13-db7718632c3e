import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { OrdersPageRoutingModule } from './orders-routing.module';
import { SharedModule } from '../../components/shared/shared.module';

import { OrdersPage } from './orders.page';
import { RejectReasonModalComponent } from '../../components/reject-reason-modal/reject-reason-modal.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    OrdersPageRoutingModule,
    SharedModule
  ],
  declarations: [OrdersPage, RejectReasonModalComponent]
})
export class OrdersPageModule {}
