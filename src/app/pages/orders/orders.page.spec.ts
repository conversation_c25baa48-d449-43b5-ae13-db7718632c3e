import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule, ModalController } from '@ionic/angular';
import { OrdersPage } from './orders.page';
import { OrderService } from '../../services/order.service';
import { UtilityService } from '../../services/utility.service';
import { LoadingService } from '../../services/loading.service';
import { ToastService } from '../../services/toast.service';
import { of } from 'rxjs';
import { Order, OrderStatus } from '../../models/order.model';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('OrdersPage', () => {
  let component: OrdersPage;
  let fixture: ComponentFixture<OrdersPage>;
  let orderServiceSpy: jasmine.SpyObj<OrderService>;
  let utilityServiceSpy: jasmine.SpyObj<UtilityService>;
  let loadingServiceSpy: jasmine.SpyObj<LoadingService>;
  let toastServiceSpy: jasmine.SpyObj<ToastService>;
  let modalControllerSpy: jasmine.SpyObj<ModalController>;

  const mockOrders: Order[] = [
    {
      id: '1',
      tableNumber: '1',
      items: [{ id: 'i1', name: 'Item 1', quantity: 1, price: 10 }],
      status: OrderStatus.PENDING,
      createdAt: new Date(),
      updatedAt: new Date(),
      total: 10
    }
  ];

  beforeEach(waitForAsync(() => {
    const orderSpy = jasmine.createSpyObj('OrderService', [
      'getOrders', 'getPendingOrders', 'acceptOrder', 'rejectOrder'
    ]);
    const utilitySpy = jasmine.createSpyObj('UtilityService', [
      'getTimeAgo', 'formatCurrency'
    ]);
    const loadingSpy = jasmine.createSpyObj('LoadingService', [
      'present', 'dismiss'
    ]);
    const toastSpy = jasmine.createSpyObj('ToastService', [
      'presentSuccessToast', 'presentErrorToast'
    ]);
    const modalSpy = jasmine.createSpyObj('ModalController', ['create']);

    TestBed.configureTestingModule({
      declarations: [OrdersPage],
      imports: [IonicModule.forRoot()],
      providers: [
        { provide: OrderService, useValue: orderSpy },
        { provide: UtilityService, useValue: utilitySpy },
        { provide: LoadingService, useValue: loadingSpy },
        { provide: ToastService, useValue: toastSpy },
        { provide: ModalController, useValue: modalSpy }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    orderServiceSpy = TestBed.inject(OrderService) as jasmine.SpyObj<OrderService>;
    utilityServiceSpy = TestBed.inject(UtilityService) as jasmine.SpyObj<UtilityService>;
    loadingServiceSpy = TestBed.inject(LoadingService) as jasmine.SpyObj<LoadingService>;
    toastServiceSpy = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    modalControllerSpy = TestBed.inject(ModalController) as jasmine.SpyObj<ModalController>;

    orderServiceSpy.getOrders.and.returnValue(of(mockOrders));
    orderServiceSpy.getPendingOrders.and.returnValue(of(mockOrders));
    orderServiceSpy.acceptOrder.and.returnValue(of(mockOrders[0]));
    orderServiceSpy.rejectOrder.and.returnValue(of(mockOrders[0]));
    
    loadingServiceSpy.present.and.returnValue(Promise.resolve());
    loadingServiceSpy.dismiss.and.returnValue(Promise.resolve(true));

    fixture = TestBed.createComponent(OrdersPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load orders on init', () => {
    component.ngOnInit();
    expect(orderServiceSpy.getOrders).toHaveBeenCalled();
    expect(loadingServiceSpy.present).toHaveBeenCalled();
  });

  it('should accept an order', async () => {
    await component.acceptOrder(mockOrders[0]);
    expect(orderServiceSpy.acceptOrder).toHaveBeenCalledWith(mockOrders[0].id);
    expect(loadingServiceSpy.present).toHaveBeenCalled();
    expect(toastServiceSpy.presentSuccessToast).toHaveBeenCalledWith('Order accepted');
  });

  it('should refresh orders', () => {
    const event = { target: { complete: jasmine.createSpy() } };
    component.doRefresh(event);
    expect(orderServiceSpy.getOrders).toHaveBeenCalled();
    expect(event.target.complete).toHaveBeenCalled();
  });

  it('should format currency using utility service', () => {
    utilityServiceSpy.formatCurrency.and.returnValue('$10.00');
    const result = component.formatCurrency(10);
    expect(utilityServiceSpy.formatCurrency).toHaveBeenCalledWith(10);
    expect(result).toBe('$10.00');
  });

  it('should get time ago using utility service', () => {
    const date = new Date();
    utilityServiceSpy.getTimeAgo.and.returnValue('Just now');
    const result = component.getTimeAgo(date);
    expect(utilityServiceSpy.getTimeAgo).toHaveBeenCalledWith(date);
    expect(result).toBe('Just now');
  });
});
