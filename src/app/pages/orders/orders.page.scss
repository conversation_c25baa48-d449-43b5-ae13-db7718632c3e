.orders-container {
  padding: 0 6px;
  margin-top: 8px;
}

.order-card {
  position: relative;
  background-color: var(--card-background);
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  display: flex;
  align-items: stretch;
}

.pending-order {
  position: relative;
}

.pending-order::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--yellow-border);
}

.order-content {
  flex: 1;
  padding: 12px;
}

.order-detail-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  color: var(--text-secondary);
  font-size: 1.2rem;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.order-table {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--text-light);
}

.order-time {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.order-id {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 12px;
}

.order-items {
  margin: 8px 0;
}

.item-row {
  display: flex;
  margin-bottom: 6px;
  align-items: flex-start;
}

.item-quantity {
  width: 30px;
  font-weight: 500;
  color: var(--text-light);
  margin-right: 8px;
  padding-left: 6px;
}

.item-details {
  flex: 1;
}

.item-name {
  font-size: 14px;
  color: var(--text-light);
  margin-bottom: 2px;
}

.modifier-row {
  margin-bottom: 2px;
}

.modifier-name {
  font-size: 12px;
  color: var(--text-secondary);
}

.modifier-price {
  font-size: 12px;
}

.item-price {
  font-size: 14px;
  color: var(--text-secondary);
  text-align: right;
  min-width: 90px;
}

.order-total {
  font-weight: bold;
  margin: 16px 0 8px;
  color: var(--text-light);
  text-align: right;
  font-size: 16px;
}

.special-instructions {
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  gap: 12px;
}

.action-buttons ion-button {
  height: 40px;
  font-size: 0.9rem;
  --border-radius: 8px;
}

.details-btn {
  --color: var(--text-light);
  --border-color: var(--text-secondary);
}

.reject-btn {
  --background: var(--button-reject);
  --color: white;
  min-width: 90px;
}

.accept-btn {
  --background: var(--button-accept);
  --color: black;
  min-width: 90px;
}

ion-refresher {
  --color: var(--text-light);
}
