# Firebase Data Population Script

This script populates your Firebase Firestore database with sample data for the Order Flow QR restaurant admin app.

## What Gets Added

- **1 Venue**: Bella Vista Restaurant
- **1 Admin User**: <EMAIL>
- **12 Tables**: Table 1-12
- **15 Products**: Pizzas, pasta, salads, burgers, desserts, and modifiers
- **6 Sample Orders**: With different statuses (pending, confirmed, rejected)

## Setup

### Method 1: Environment Variables (Recommended)

Set these environment variables with your Firebase configuration:

```bash
export FIREBASE_API_KEY="your-api-key"
export FIREBASE_AUTH_DOMAIN="your-project.firebaseapp.com"
export FIREBASE_PROJECT_ID="your-project-id"
export FIREBASE_STORAGE_BUCKET="your-project.appspot.com"
export FIREBASE_MESSAGING_SENDER_ID="123456789"
export FIREBASE_APP_ID="your-app-id"
```

### Method 2: <PERSON>t Directly

Open `src/app/scripts/populate-firebase.ts` and update the `firebaseConfig` object with your actual Firebase configuration.

## Usage

### Install Dependencies

```bash
npm install
```

### Run the Script

```bash
npm run populate-firebase
```

## Sample Data Details

### Orders Created

1. **Order ORD-001** (Table 12, Pending)
   - Margherita Pizza + Extra Cheese
   - Garlic Bread
   - Total: 19.48 RON

2. **Order ORD-002** (Table 8, Pending)
   - Chicken Alfredo + No Onions + Extra Cheese
   - Total: 20.99 RON

3. **Order ORD-003** (Table 5, Pending)
   - Caesar Salad + Grilled Salmon
   - Total: 33.98 RON

4. **Order ORD-004** (Table 3, Confirmed)
   - 2x Beef Burger
   - Total: 33.98 RON

5. **Order ORD-005** (Table 7, Rejected)
   - Vegetarian Pizza
   - Total: 16.99 RON

6. **Order ORD-006** (Table 2, Confirmed)
   - Fish and Chips + Tiramisu
   - Total: 27.98 RON

### Login Credentials

After running the script, you can login to the app with:
- **Email**: <EMAIL>
- **Password**: admin123

## Firestore Structure

The script creates the following collections:

- `venues` - Restaurant venues
- `waiters` - Staff members
- `tables` - Restaurant tables
- `products` - Menu items and modifiers
- `orders` - Customer orders
- `orders/{orderId}/order_items` - Order items (subcollection)

## Troubleshooting

### Permission Errors

Make sure your Firebase project has the correct Firestore security rules that allow writing data.

### Configuration Errors

If you see "Please update the Firebase configuration", make sure you've either:
1. Set the environment variables correctly, or
2. Updated the `firebaseConfig` object in the script

### Network Errors

Ensure you have internet connectivity and your Firebase project is accessible.

## Notes

- The script will overwrite existing data with the same IDs
- Run this script only once or when you want to reset your data
- The app will work entirely with this Firebase data (no mock data)
