import { Component, Input, OnInit } from '@angular/core';
import { Order, OrderItem } from '../../models/order.model';

@Component({
    selector: 'app-order-summary',
    templateUrl: './order-summary.component.html',
    styleUrls: ['./order-summary.component.scss'],
    standalone: false
})
export class OrderSummaryComponent implements OnInit {
  @Input() order!: Order;
  @Input() orderItems: OrderItem[] = [];
  @Input() showSpecialInstructions: boolean = true;
  @Input() showRejectionReason: boolean = false;

  constructor() { }

  ngOnInit() {
    console.log('OrderSummaryComponent - Order:', this.order);
    console.log('OrderSummaryComponent - Items:', this.order?.items);
  }

  formatCurrency(amount: number | undefined): string {
    if (amount === undefined || amount === null || isNaN(amount)) {
      return '0.00 RON';
    }
    return `${amount.toFixed(2)} RON`;
  }
}
