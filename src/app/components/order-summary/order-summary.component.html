<div class="order-summary">
  <!-- Debug info -->
  <div *ngIf="!order?.items || order.items.length === 0" style="color: red; font-size: 12px; margin-bottom: 8px;">
    DEBUG: No items found. Items: {{ order.items?.length || 0 }}
  </div>

  <div class="order-items">
    <!-- Main items -->
    <app-order-item *ngFor="let item of order.items"
      [quantity]="item.quantity + 'x'"
      [name]="item.name"
      [price]="item.price">
    </app-order-item>

    <!-- Modifiers for each item -->
    <ng-container *ngFor="let item of order.items">
      <app-order-item *ngFor="let modifier of (item.modifiers || [])"
        [name]="modifier.name"
        [price]="modifier.price"
        [isModifier]="true"
        [showPrice]="modifier.price > 0">
      </app-order-item>

      <!-- Item notes -->
      <app-order-item *ngIf="item.notes"
        [name]="item.notes"
        [isModifier]="true"
        [showPrice]="false">
      </app-order-item>
    </ng-container>
  </div>

  <div class="order-total">
    {{ formatCurrency(order.total) }}
  </div>

  <div *ngIf="showSpecialInstructions && order.specialInstructions" class="special-instructions">
    {{ order.specialInstructions }}
  </div>

  <div *ngIf="showRejectionReason && order.rejectionReason" class="rejection-reason">
    Reason: {{ order.rejectionReason }}
  </div>
</div>
