.user-menu {
  min-width: 250px;
  padding: 0;
}

.venue-section {
  background: var(--ion-color-primary);
  color: var(--ion-color-primary-contrast);
  padding: 16px;
  text-align: center;
}

.venue-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 4px;
}

.user-section {
  padding: 16px;
  border-bottom: 1px solid var(--ion-color-light);
}

.user-info {
  text-align: center;
}

.user-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--ion-color-dark);
  margin-bottom: 4px;
}

.user-role {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  background: var(--ion-color-light);
  padding: 4px 12px;
  border-radius: 12px;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-actions {
  padding: 8px 0;
}

.menu-actions ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 48px;
}

.menu-actions ion-icon {
  color: var(--ion-color-danger);
}

.menu-actions ion-label {
  color: var(--ion-color-danger);
  font-weight: 500;
}
