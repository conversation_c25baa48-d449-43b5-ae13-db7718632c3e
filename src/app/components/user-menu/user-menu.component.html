<div class="menu-content">
  <!-- Venue Information -->
  <div class="venue-section" *ngIf="currentVenue">
    <div class="venue-name">{{ currentVenue!.name }}</div>
  </div>

  <!-- User Information -->
  <div class="user-section" *ngIf="currentWaiter">
    <div class="user-info">
      <div class="user-name">{{ getWaiterDisplayName() }}</div>
      <div class="user-role">{{ getWaiterRole() }}</div>
    </div>
  </div>
</div>

<!-- Logout Button at Bottom -->
<div class="logout-section">
  <ion-list>
    <ion-item button (click)="logout()" lines="none">
      <ion-icon name="log-out-outline" slot="start" color="danger"></ion-icon>
      <ion-label color="danger">Logout</ion-label>
    </ion-item>
  </ion-list>
</div>
