<div class="user-menu">
  <!-- Venue Information -->
  <div class="venue-section" *ngIf="currentVenue">
    <div class="venue-name">{{ currentVenue!.name }}</div>
  </div>

  <!-- User Information -->
  <div class="user-section" *ngIf="currentWaiter">
    <div class="user-info">
      <div class="user-name">{{ getWaiterDisplayName() }}</div>
      <div class="user-role">{{ getWaiterRole() }}</div>
    </div>
  </div>

  <!-- Menu Actions -->
  <div class="menu-actions">
    <ion-item button (click)="logout()" lines="none">
      <ion-icon name="log-out-outline" slot="start"></ion-icon>
      <ion-label>Logout</ion-label>
    </ion-item>
  </div>
</div>
