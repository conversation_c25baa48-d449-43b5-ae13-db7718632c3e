import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { MenuController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { UserService } from '../../services/user.service';
import { AuthService } from '../../services/auth.service';
import { Venue, Waiter } from '../../models/order.model';

@Component({
  selector: 'app-user-menu',
  templateUrl: './user-menu.component.html',
  styleUrls: ['./user-menu.component.scss'],
  standalone: false
})
export class UserMenuComponent implements OnInit, OnDestroy {
  currentVenue: Venue | null = null;
  currentWaiter: Waiter | null = null;
  private subscriptions: Subscription[] = [];

  constructor(
    private menuController: MenuController,
    private userService: UserService,
    private authService: AuthService
  ) { }

  ngOnInit() {
    // Subscribe to venue changes
    this.subscriptions.push(
      this.userService.currentVenue$.subscribe(venue => {
        this.currentVenue = venue;
      })
    );

    // Subscribe to waiter changes
    this.subscriptions.push(
      this.userService.currentWaiter$.subscribe(waiter => {
        this.currentWaiter = waiter;
      })
    );
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  getWaiterDisplayName(): string {
    return this.userService.getWaiterDisplayName();
  }

  getWaiterRole(): string {
    if (this.currentWaiter) {
      return this.currentWaiter.role.charAt(0).toUpperCase() + this.currentWaiter.role.slice(1);
    }
    return '';
  }

  async logout() {
    await this.menuController.close('user-menu');
    await this.authService.logout();
  }

  async closeMenu() {
    await this.menuController.close('user-menu');
  }
}
