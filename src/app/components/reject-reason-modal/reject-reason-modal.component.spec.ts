import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule, ModalController } from '@ionic/angular';
import { FormsModule } from '@angular/forms';
import { RejectReasonModalComponent } from './reject-reason-modal.component';
import { RejectionReason } from '../../models/order.model';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('RejectReasonModalComponent', () => {
  let component: RejectReasonModalComponent;
  let fixture: ComponentFixture<RejectReasonModalComponent>;
  let modalControllerSpy: jasmine.SpyObj<ModalController>;

  beforeEach(waitForAsync(() => {
    const modalSpy = jasmine.createSpyObj('ModalController', ['dismiss']);

    TestBed.configureTestingModule({
      declarations: [RejectReasonModalComponent],
      imports: [IonicModule.forRoot(), FormsModule],
      providers: [
        { provide: ModalController, useValue: modalSpy }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    modalControllerSpy = TestBed.inject(ModalController) as jasmine.SpyObj<ModalController>;

    fixture = TestBed.createComponent(RejectReasonModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have all rejection reasons', () => {
    expect(component.reasons.length).toBe(Object.values(RejectionReason).length);
    expect(component.reasons).toEqual(Object.values(RejectionReason));
  });

  it('should select a reason', () => {
    const reason = RejectionReason.OUT_OF_STOCK;
    component.selectReason(reason);
    expect(component.selectedReason).toBe(reason);
  });

  it('should dismiss modal on cancel', () => {
    component.cancel();
    expect(modalControllerSpy.dismiss).toHaveBeenCalled();
  });

  it('should dismiss modal with data on confirm', () => {
    const reason = RejectionReason.OUT_OF_STOCK;
    const note = 'Test note';
    
    component.selectedReason = reason;
    component.note = note;
    
    component.confirm();
    
    expect(modalControllerSpy.dismiss).toHaveBeenCalledWith({
      reason: reason,
      note: note
    });
  });

  it('should not dismiss modal on confirm if no reason selected', () => {
    component.selectedReason = null;
    component.note = 'Test note';
    
    component.confirm();
    
    expect(modalControllerSpy.dismiss).not.toHaveBeenCalled();
  });
});
