import { Component, OnInit } from '@angular/core';
import { <PERSON>dalController } from '@ionic/angular';
import { RejectionReason } from '../../models/order.model';

@Component({
    selector: 'app-reject-reason-modal',
    templateUrl: './reject-reason-modal.component.html',
    styleUrls: ['./reject-reason-modal.component.scss'],
    standalone: false
})
export class RejectReasonModalComponent implements OnInit {
  reasons = Object.values(RejectionReason);
  selectedReason: RejectionReason | null = null;
  note: string = '';

  constructor(private modalController: ModalController) { }

  ngOnInit() {}

  cancel() {
    this.modalController.dismiss();
  }

  confirm() {
    if (this.selectedReason) {
      this.modalController.dismiss({
        reason: this.selectedReason,
        note: this.note
      });
    }
  }
}
