<ion-header>
  <ion-toolbar>
    <ion-title>Rejection Reason</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="cancel()">
        <ion-icon name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list>
    <ion-radio-group>
      <ion-list-header>
        <ion-label>Select a reason</ion-label>
      </ion-list-header>

      <ion-item *ngFor="let reason of reasons">
        <ion-label>{{ reason }}</ion-label>
        <ion-radio [value]="reason" (click)="selectReason(reason)"></ion-radio>
      </ion-item>
    </ion-radio-group>
  </ion-list>

  <ion-item class="additional-notes-item">
    <ion-label position="stacked">Additional notes (optional)</ion-label>
    <ion-textarea [(ngModel)]="note" rows="3" placeholder="Enter additional details..."></ion-textarea>
  </ion-item>
</ion-content>

<ion-footer>
  <ion-toolbar>
    <ion-buttons slot="end">
      <ion-button (click)="cancel()" color="medium">
        Cancel
      </ion-button>
      <ion-button (click)="confirm()" [disabled]="!selectedReason" color="danger">
        Confirm Rejection
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-footer>
