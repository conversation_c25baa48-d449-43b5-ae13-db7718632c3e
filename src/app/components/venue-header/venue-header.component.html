<div class="venue-header" *ngIf="currentVenue">
  <div class="venue-info">
    <h2 class="venue-name">{{ currentVenue!.name }}</h2>
    <div class="waiter-info" *ngIf="currentWaiter">
      <span class="waiter-name">{{ getWaiterDisplayName() }}</span>
      <span class="waiter-role">{{ getWaiterRole() }}</span>
    </div>
  </div>
</div>

<div class="venue-header loading" *ngIf="!currentVenue">
  <div class="venue-info">
    <h2 class="venue-name">Loading...</h2>
  </div>
</div>
