.venue-header {
  padding: 16px;
  background: var(--ion-color-primary);
  color: white;
  
  &.loading {
    opacity: 0.7;
  }
}

.venue-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.venue-name {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.waiter-info {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 0.9rem;
  opacity: 0.9;
}

.waiter-name {
  font-weight: 500;
}

.waiter-role {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
