import { Component } from '@angular/core';
import { OrderService } from '../services/order.service';
import { AuthService } from '../services/auth.service';
import { UserService } from '../services/user.service';
import { MenuController } from '@ionic/angular';
import { map } from 'rxjs/operators';
import { Observable, of } from 'rxjs';

@Component({
    selector: 'app-tabs',
    templateUrl: 'tabs.page.html',
    styleUrls: ['tabs.page.scss'],
    standalone: false
})
export class TabsPage {
  pendingOrdersCount$: Observable<number> = of(0);

  constructor(
    private orderService: OrderService,
    private authService: AuthService,
    private userService: UserService,
    private menuController: MenuController
  ) {
    this.pendingOrdersCount$ = this.orderService.getPendingOrders().pipe(
      map(orders => orders.length)
    );
  }

  async openUserMenu() {
    await this.menuController.open('user-menu');
  }

  async logout() {
    await this.authService.logout();
  }
}
