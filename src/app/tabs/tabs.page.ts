import { Component } from '@angular/core';
import { OrderService } from '../services/order.service';
import { AuthService } from '../services/auth.service';
import { UserService } from '../services/user.service';
import { PopoverController } from '@ionic/angular';
import { map } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { UserMenuComponent } from '../components/user-menu/user-menu.component';

@Component({
    selector: 'app-tabs',
    templateUrl: 'tabs.page.html',
    styleUrls: ['tabs.page.scss'],
    standalone: false
})
export class TabsPage {
  pendingOrdersCount$: Observable<number> = of(0);

  constructor(
    private orderService: OrderService,
    private authService: AuthService,
    private userService: UserService,
    private popoverController: PopoverController
  ) {
    this.pendingOrdersCount$ = this.orderService.getPendingOrders().pipe(
      map(orders => orders.length)
    );
  }

  async openUserMenu(event: any) {
    const popover = await this.popoverController.create({
      component: UserMenuComponent,
      event: event,
      translucent: true,
      showBackdrop: true,
      side: 'end',
      alignment: 'end'
    });

    await popover.present();
  }

  async logout() {
    await this.authService.logout();
  }
}
