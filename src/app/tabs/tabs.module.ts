import { IonicModule } from '@ionic/angular';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { TabsPageRoutingModule } from './tabs-routing.module';

import { TabsPage } from './tabs.page';
import { VenueHeaderComponent } from '../components/venue-header/venue-header.component';
import { UserMenuComponent } from '../components/user-menu/user-menu.component';

@NgModule({
  imports: [
    IonicModule,
    CommonModule,
    FormsModule,
    TabsPageRoutingModule
  ],
  declarations: [TabsPage, VenueHeaderComponent, UserMenuComponent]
})
export class TabsPageModule {}
