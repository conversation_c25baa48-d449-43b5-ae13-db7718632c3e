<ion-menu side="end" menuId="user-menu" contentId="main-content">
  <ion-header>
    <ion-toolbar color="primary">
      <ion-title>User Menu</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-content>
    <app-user-menu></app-user-menu>
  </ion-content>
</ion-menu>

<div id="main-content">
  <ion-header>
    <ion-toolbar>
      <ion-title>Order Flow QR</ion-title>
      <ion-buttons slot="end">
        <ion-button (click)="openUserMenu()" fill="clear">
          <ion-icon name="menu-outline" slot="icon-only"></ion-icon>
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>

  <ion-tabs>
  <ion-tab-bar slot="bottom">
    <ion-tab-button tab="orders">
      <ion-icon name="restaurant-outline"></ion-icon>
      <ion-label>Orders</ion-label>
      <ng-container *ngIf="pendingOrdersCount$ | async as count">
        <ion-badge *ngIf="count > 0">{{ count }}</ion-badge>
      </ng-container>
    </ion-tab-button>

    <ion-tab-button tab="history">
      <ion-icon name="time-outline"></ion-icon>
      <ion-label>History</ion-label>
    </ion-tab-button>
  </ion-tab-bar>
</ion-tabs>
</div>
