<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Populate Firebase Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #d6052f;
            text-align: center;
        }
        .button {
            background-color: #d6052f;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            display: block;
            width: 100%;
        }
        .button:hover {
            background-color: #bc0429;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Firebase Data Population</h1>
        <p>This script will populate your Firebase Firestore with sample data for the Order Flow QR app.</p>
        
        <h3>What will be added:</h3>
        <ul>
            <li><strong>1 Venue:</strong> Bella Vista Restaurant</li>
            <li><strong>1 Waiter:</strong> Admin User (<EMAIL>)</li>
            <li><strong>12 Tables:</strong> Table 1-12</li>
            <li><strong>15 Products:</strong> Pizzas, pasta, salads, burgers, etc.</li>
            <li><strong>6 Sample Orders:</strong> With different statuses and items</li>
        </ul>

        <button id="populateBtn" class="button" onclick="populateData()">
            🚀 Populate Firebase Data
        </button>

        <button id="clearBtn" class="button" onclick="clearLog()" style="background-color: #6c757d;">
            🗑️ Clear Log
        </button>

        <div id="log" class="log"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, doc, setDoc, Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Firebase configuration - UPDATE THIS WITH YOUR CONFIG
        const firebaseConfig = {
            apiKey: "your-api-key",
            authDomain: "your-project.firebaseapp.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "123456789",
            appId: "your-app-id"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        window.clearLog = function() {
            document.getElementById('log').innerHTML = '';
        }

        window.populateData = async function() {
            const btn = document.getElementById('populateBtn');
            btn.disabled = true;
            btn.textContent = '⏳ Populating...';
            
            try {
                log('🚀 Starting Firebase data population...', 'info');

                // Sample data
                const venues = [
                    { id: 'venue_001', name: 'Bella Vista Restaurant' }
                ];

                const waiters = [
                    {
                        id: 'waiter_001',
                        venueId: 'venue_001',
                        email: '<EMAIL>',
                        name: { first: 'Admin', last: 'User' },
                        role: 'admin',
                        permissions: {
                            canViewOrders: true,
                            canUpdateOrders: true,
                            canDeleteOrders: true,
                            canManageMenu: true,
                            canManageStaff: true
                        },
                        isActive: true,
                        lastLogin: new Date()
                    }
                ];

                const tables = [];
                for (let i = 1; i <= 12; i++) {
                    tables.push({
                        id: `table_${i.toString().padStart(3, '0')}`,
                        venueId: 'venue_001',
                        tableNumber: i.toString(),
                        name: `Table ${i}`
                    });
                }

                const products = [
                    { id: 'product_001', venueId: 'venue_001', name: 'Margherita Pizza', description: 'Classic pizza with tomato sauce, mozzarella, and basil', price: 12.99, currency: 'RON', isModifier: false, isActive: true },
                    { id: 'product_002', venueId: 'venue_001', name: 'Pepperoni Pizza', description: 'Pizza with tomato sauce, mozzarella, and pepperoni', price: 15.99, currency: 'RON', isModifier: false, isActive: true },
                    { id: 'product_003', venueId: 'venue_001', name: 'Vegetarian Pizza', description: 'Pizza with vegetables and cheese', price: 16.99, currency: 'RON', isModifier: false, isActive: true },
                    { id: 'product_004', venueId: 'venue_001', name: 'Chicken Alfredo', description: 'Creamy pasta with grilled chicken', price: 18.99, currency: 'RON', isModifier: false, isActive: true },
                    { id: 'product_005', venueId: 'venue_001', name: 'Caesar Salad', description: 'Fresh romaine lettuce with Caesar dressing', price: 8.99, currency: 'RON', isModifier: false, isActive: true },
                    { id: 'product_006', venueId: 'venue_001', name: 'Grilled Salmon', description: 'Fresh Atlantic salmon grilled to perfection', price: 24.99, currency: 'RON', isModifier: false, isActive: true },
                    { id: 'product_007', venueId: 'venue_001', name: 'Beef Burger', description: 'Juicy beef burger with lettuce, tomato, and cheese', price: 16.99, currency: 'RON', isModifier: false, isActive: true },
                    { id: 'product_008', venueId: 'venue_001', name: 'Fish and Chips', description: 'Beer-battered fish with crispy fries', price: 19.99, currency: 'RON', isModifier: false, isActive: true },
                    { id: 'product_009', venueId: 'venue_001', name: 'Garlic Bread', description: 'Toasted bread with garlic butter', price: 4.99, currency: 'RON', isModifier: false, isActive: true },
                    { id: 'product_010', venueId: 'venue_001', name: 'Tiramisu', description: 'Classic Italian dessert', price: 7.99, currency: 'RON', isModifier: false, isActive: true },
                    // Modifiers
                    { id: 'modifier_001', venueId: 'venue_001', name: 'Extra Cheese', description: 'Additional cheese', price: 1.50, currency: 'RON', isModifier: true, isActive: true },
                    { id: 'modifier_002', venueId: 'venue_001', name: 'Extra Pepperoni', description: 'Additional pepperoni', price: 2.00, currency: 'RON', isModifier: true, isActive: true },
                    { id: 'modifier_003', venueId: 'venue_001', name: 'No Onions', description: 'Remove onions', price: 0, currency: 'RON', isModifier: true, isActive: true },
                    { id: 'modifier_004', venueId: 'venue_001', name: 'Extra Sauce', description: 'Additional sauce', price: 0.50, currency: 'RON', isModifier: true, isActive: true },
                    { id: 'modifier_005', venueId: 'venue_001', name: 'Gluten Free Base', description: 'Gluten-free pizza base', price: 3.00, currency: 'RON', isModifier: true, isActive: true }
                ];

                // Add venues
                for (const venue of venues) {
                    await setDoc(doc(db, 'venues', venue.id), venue);
                    log(`✅ Added venue: ${venue.name}`, 'success');
                }

                // Add waiters
                for (const waiter of waiters) {
                    await setDoc(doc(db, 'waiters', waiter.id), waiter);
                    log(`✅ Added waiter: ${waiter.name.first} ${waiter.name.last}`, 'success');
                }

                // Add tables
                for (const table of tables) {
                    await setDoc(doc(db, 'tables', table.id), table);
                    log(`✅ Added table: ${table.name}`, 'success');
                }

                // Add products
                for (const product of products) {
                    await setDoc(doc(db, 'products', product.id), product);
                    log(`✅ Added product: ${product.name}`, 'success');
                }

                log('📦 Basic data populated successfully!', 'success');
                log('🍽️ Now adding sample orders...', 'info');

                // Add sample orders
                await addSampleOrders();

                log('🎉 Firebase data population completed successfully!', 'success');
                log('🔑 You can now login with: <EMAIL>', 'info');

            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
                console.error('Error populating Firebase data:', error);
            } finally {
                btn.disabled = false;
                btn.textContent = '🚀 Populate Firebase Data';
            }
        }

        async function addSampleOrders() {
            const orders = [
                {
                    id: 'order_001',
                    venueId: 'venue_001',
                    tableId: 'table_012',
                    tableName: 'Table 12',
                    orderNumber: 'ORD-001',
                    status: 'pending',
                    totals: { subtotal: 19.48, tips: 0, discount: 0, total: 19.48, currency: 'RON' },
                    specialInstructions: 'Extra crispy crust',
                    createdAt: Timestamp.fromDate(new Date(Date.now() - 5 * 60000)),
                    updatedAt: Timestamp.fromDate(new Date(Date.now() - 5 * 60000))
                },
                {
                    id: 'order_002',
                    venueId: 'venue_001',
                    tableId: 'table_008',
                    tableName: 'Table 8',
                    orderNumber: 'ORD-002',
                    status: 'pending',
                    totals: { subtotal: 20.99, tips: 0, discount: 0, total: 20.99, currency: 'RON' },
                    specialInstructions: 'No onions please',
                    createdAt: Timestamp.fromDate(new Date(Date.now() - 12 * 60000)),
                    updatedAt: Timestamp.fromDate(new Date(Date.now() - 12 * 60000))
                },
                {
                    id: 'order_003',
                    venueId: 'venue_001',
                    tableId: 'table_005',
                    tableName: 'Table 5',
                    orderNumber: 'ORD-003',
                    status: 'pending',
                    totals: { subtotal: 33.98, tips: 0, discount: 0, total: 33.98, currency: 'RON' },
                    createdAt: Timestamp.fromDate(new Date(Date.now() - 8 * 60000)),
                    updatedAt: Timestamp.fromDate(new Date(Date.now() - 8 * 60000))
                }
            ];

            const orderItems = {
                'order_001': [
                    { id: 'item_001', productId: 'product_001', productName: 'Margherita Pizza', quantity: 1, unitPrice: 12.99, totalPrice: 12.99, currency: 'RON', createdAt: Timestamp.now(), updatedAt: Timestamp.now() },
                    { id: 'item_002', productId: 'modifier_001', productName: 'Extra Cheese', quantity: 1, unitPrice: 1.50, totalPrice: 1.50, currency: 'RON', createdAt: Timestamp.now(), updatedAt: Timestamp.now() },
                    { id: 'item_003', productId: 'product_009', productName: 'Garlic Bread', quantity: 1, unitPrice: 4.99, totalPrice: 4.99, currency: 'RON', createdAt: Timestamp.now(), updatedAt: Timestamp.now() }
                ],
                'order_002': [
                    { id: 'item_004', productId: 'product_004', productName: 'Chicken Alfredo', quantity: 1, unitPrice: 18.99, totalPrice: 18.99, currency: 'RON', createdAt: Timestamp.now(), updatedAt: Timestamp.now() },
                    { id: 'item_005', productId: 'modifier_003', productName: 'No Onions', quantity: 1, unitPrice: 0, totalPrice: 0, currency: 'RON', createdAt: Timestamp.now(), updatedAt: Timestamp.now() },
                    { id: 'item_006', productId: 'modifier_001', productName: 'Extra Cheese', quantity: 1, unitPrice: 2.00, totalPrice: 2.00, currency: 'RON', createdAt: Timestamp.now(), updatedAt: Timestamp.now() }
                ],
                'order_003': [
                    { id: 'item_007', productId: 'product_005', productName: 'Caesar Salad', quantity: 1, unitPrice: 8.99, totalPrice: 8.99, currency: 'RON', createdAt: Timestamp.now(), updatedAt: Timestamp.now() },
                    { id: 'item_008', productId: 'product_006', productName: 'Grilled Salmon', quantity: 1, unitPrice: 24.99, totalPrice: 24.99, currency: 'RON', createdAt: Timestamp.now(), updatedAt: Timestamp.now() }
                ]
            };

            // Add orders and their items
            for (const order of orders) {
                await setDoc(doc(db, 'orders', order.id), order);
                log(`✅ Added order: ${order.orderNumber}`, 'success');

                // Add order items as subcollection
                const items = orderItems[order.id];
                if (items) {
                    for (const item of items) {
                        await setDoc(doc(db, 'orders', order.id, 'order_items', item.id), item);
                        log(`  ➕ Added item: ${item.productName}`, 'success');
                    }
                }
            }
        }
    </script>
</body>
</html>
