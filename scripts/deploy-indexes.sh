#!/bin/bash

echo "🚀 Deploying Firestore Indexes..."
echo "=================================="

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed."
    echo "📦 Install it with: npm install -g firebase-tools"
    echo "🔐 Then login with: firebase login"
    exit 1
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "❌ You are not logged in to Firebase."
    echo "🔐 Please run: firebase login"
    exit 1
fi

echo "📋 Deploying indexes from firestore.indexes.json..."

# Deploy the indexes
firebase deploy --only firestore:indexes

if [ $? -eq 0 ]; then
    echo "✅ Firestore indexes deployed successfully!"
    echo ""
    echo "🔍 You can view your indexes at:"
    echo "https://console.firebase.google.com/project/orderflowqr/firestore/indexes"
    echo ""
    echo "⏱️  Note: Index creation may take a few minutes to complete."
else
    echo "❌ Failed to deploy indexes."
    echo "💡 Make sure you have the correct permissions and project setup."
fi
