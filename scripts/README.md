# Firebase Data Population Script

This script populates your Firebase Firestore database with sample data for the Order Flow QR restaurant admin app.

## What Gets Added

- **1 Venue**: Bella Vista Restaurant
- **1 Admin User**: <EMAIL>
- **12 Tables**: Table 1-12
- **15 Products**: Pizzas, pasta, salads, burgers, desserts, and modifiers
- **6 Sample Orders**: With different statuses (pending, confirmed, rejected)

## Setup

The script automatically uses the Firebase configuration from `src/environments/environment.ts`. No additional setup required!

## Usage

### Install Dependencies

```bash
npm install
```

### Run the Script

```bash
npm run populate-firebase
```

### Expected Output

```
🔥 Firebase Data Population Script
=====================================
📡 Using Firebase project: orderflowqr

Starting Firebase data population...
✅ Added venue: Bella Vista Restaurant
✅ Added waiter: Admin User
✅ Added table: Table 1
...
✅ Added product: Margherita Pizza
...
📦 Basic data populated successfully!
🍽️ Now adding sample orders...
✅ Added order: ORD-001
  ➕ Added item: Margherita Pizza
  ➕ Added item: Extra Cheese
...

🎉 Success! Firebase data population completed.
🔑 You can now login with: <EMAIL>
```

## Sample Data Details

### Orders Created

1. **Order ORD-001** (Table 12, Pending)
   - Margherita Pizza + Extra Cheese
   - Garlic Bread
   - Total: 19.48 RON

2. **Order ORD-002** (Table 8, Pending)
   - Chicken Alfredo + No Onions + Extra Cheese
   - Total: 20.99 RON

3. **Order ORD-003** (Table 5, Pending)
   - Caesar Salad + Grilled Salmon
   - Total: 33.98 RON

4. **Order ORD-004** (Table 3, Confirmed)
   - 2x Beef Burger
   - Total: 33.98 RON

5. **Order ORD-005** (Table 7, Rejected)
   - Vegetarian Pizza
   - Total: 16.99 RON

6. **Order ORD-006** (Table 2, Confirmed)
   - Fish and Chips + Tiramisu
   - Total: 27.98 RON

### Login Credentials

After running the script, you can login to the app with:
- **Email**: <EMAIL>
- **Password**: admin123

## Firestore Structure

The script creates the following collections:

- `venues` - Restaurant venues
- `waiters` - Staff members
- `tables` - Restaurant tables
- `products` - Menu items and modifiers
- `orders` - Customer orders
- `orders/{orderId}/order_items` - Order items (subcollection)

## Troubleshooting

### Permission Errors

Make sure your Firebase project has the correct Firestore security rules that allow writing data.

### Configuration Errors

The script uses the Firebase configuration from `src/environments/environment.ts`. Make sure this file contains valid Firebase credentials.

### Network Errors

Ensure you have internet connectivity and your Firebase project is accessible.

## Notes

- The script will overwrite existing data with the same IDs
- Run this script only once or when you want to reset your data
- The app will work entirely with this Firebase data (no mock data)
