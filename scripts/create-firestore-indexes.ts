const { initializeApp } = require('firebase/app');
const { getFirestore } = require('firebase/firestore');

// Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyBCAxpWlrKWKu9qyzGkXSVLm_uwmXP3HCU",
  authDomain: "orderflowqr.firebaseapp.com",
  projectId: "orderflowqr",
  storageBucket: "orderflowqr.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:3220d218233f03826445bd"
};

async function createFirestoreIndexes() {
  console.log('🔍 Firestore Indexes Setup Guide');
  console.log('=====================================\n');

  // Initialize Firebase
  const app = initializeApp(firebaseConfig);
  const db = getFirestore(app);

  console.log('📋 Required Firestore Indexes for Order Flow QR\n');
  console.log('Based on your current queries, you need to create the following composite indexes:\n');

  console.log('🏢 1. ORDERS COLLECTION INDEXES');
  console.log('--------------------------------');
  console.log('Index 1: venueId + createdAt');
  console.log('  Collection: orders');
  console.log('  Fields:');
  console.log('    - venueId (Ascending)');
  console.log('    - createdAt (Descending)');
  console.log('  Purpose: Main query for loading orders by venue\n');

  console.log('Index 2: venueId + status + createdAt');
  console.log('  Collection: orders');
  console.log('  Fields:');
  console.log('    - venueId (Ascending)');
  console.log('    - status (Ascending)');
  console.log('    - createdAt (Descending)');
  console.log('  Purpose: Filter orders by venue and status (pending, confirmed, rejected)\n');

  console.log('Index 3: venueId + tableId + createdAt');
  console.log('  Collection: orders');
  console.log('  Fields:');
  console.log('    - venueId (Ascending)');
  console.log('    - tableId (Ascending)');
  console.log('    - createdAt (Descending)');
  console.log('  Purpose: Filter orders by venue and table\n');

  console.log('Index 4: venueId + assignedWaiter + status');
  console.log('  Collection: orders');
  console.log('  Fields:');
  console.log('    - venueId (Ascending)');
  console.log('    - assignedWaiter (Ascending)');
  console.log('    - status (Ascending)');
  console.log('  Purpose: Filter orders by venue, waiter, and status\n');

  console.log('👥 2. WAITERS COLLECTION INDEXES');
  console.log('--------------------------------');
  console.log('Index 5: email');
  console.log('  Collection: waiters');
  console.log('  Fields:');
  console.log('    - email (Ascending)');
  console.log('  Purpose: User authentication lookup by email\n');

  console.log('Index 6: venueId + role');
  console.log('  Collection: waiters');
  console.log('  Fields:');
  console.log('    - venueId (Ascending)');
  console.log('    - role (Ascending)');
  console.log('  Purpose: Filter waiters by venue and role\n');

  console.log('🍽️ 3. PRODUCTS COLLECTION INDEXES');
  console.log('----------------------------------');
  console.log('Index 7: venueId + isActive');
  console.log('  Collection: products');
  console.log('  Fields:');
  console.log('    - venueId (Ascending)');
  console.log('    - isActive (Ascending)');
  console.log('  Purpose: Filter active products by venue\n');

  console.log('Index 8: venueId + isModifier + isActive');
  console.log('  Collection: products');
  console.log('  Fields:');
  console.log('    - venueId (Ascending)');
  console.log('    - isModifier (Ascending)');
  console.log('    - isActive (Ascending)');
  console.log('  Purpose: Filter products vs modifiers by venue\n');

  console.log('🪑 4. TABLES COLLECTION INDEXES');
  console.log('-------------------------------');
  console.log('Index 9: venueId');
  console.log('  Collection: tables');
  console.log('  Fields:');
  console.log('    - venueId (Ascending)');
  console.log('  Purpose: Filter tables by venue\n');

  console.log('📱 HOW TO CREATE THESE INDEXES:');
  console.log('===============================');
  console.log('1. Go to Firebase Console: https://console.firebase.google.com');
  console.log('2. Select your project: orderflowqr');
  console.log('3. Go to Firestore Database > Indexes');
  console.log('4. Click "Create Index" for each index above');
  console.log('5. Enter the collection name and fields as specified\n');

  console.log('⚡ AUTOMATIC INDEX CREATION:');
  console.log('============================');
  console.log('Alternatively, you can trigger automatic index creation by:');
  console.log('1. Running your app and performing the queries');
  console.log('2. Firestore will show index creation links in the console');
  console.log('3. Click the links to automatically create the required indexes\n');

  console.log('🎯 PRIORITY ORDER:');
  console.log('==================');
  console.log('Create these indexes first (most important):');
  console.log('1. orders: venueId + createdAt');
  console.log('2. waiters: email');
  console.log('3. orders: venueId + status + createdAt');
  console.log('4. products: venueId + isActive');
  console.log('5. tables: venueId\n');

  console.log('✅ Index creation guide completed!');
  console.log('Your app will perform much better once these indexes are created.');
}

// Run the guide
createFirestoreIndexes();
