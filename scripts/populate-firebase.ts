#!/usr/bin/env node

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, doc, setDoc, Timestamp } from 'firebase/firestore';
import { environment } from '../src/environments/environment';

// Initialize Firebase using environment configuration
const app = initializeApp(environment.firebase);
const db = getFirestore(app);

// Sample data
const venues = [
  {
    id: 'venue_001',
    name: 'Bella Vista Restaurant'
  }
];

const waiters = [
  {
    id: 'waiter_001',
    venueId: 'venue_001',
    email: '<EMAIL>',
    name: {
      first: 'Admin',
      last: 'User'
    },
    role: 'admin',
    permissions: {
      canViewOrders: true,
      canUpdateOrders: true,
      canDeleteOrders: true,
      canManageMenu: true,
      canManageStaff: true
    },
    isActive: true,
    lastLogin: new Date()
  }
];

const tables = [
  { id: 'table_001', venueId: 'venue_001', tableNumber: '1', name: 'Table 1' },
  { id: 'table_002', venueId: 'venue_001', tableNumber: '2', name: 'Table 2' },
  { id: 'table_003', venueId: 'venue_001', tableNumber: '3', name: 'Table 3' },
  { id: 'table_004', venueId: 'venue_001', tableNumber: '4', name: 'Table 4' },
  { id: 'table_005', venueId: 'venue_001', tableNumber: '5', name: 'Table 5' },
  { id: 'table_006', venueId: 'venue_001', tableNumber: '6', name: 'Table 6' },
  { id: 'table_007', venueId: 'venue_001', tableNumber: '7', name: 'Table 7' },
  { id: 'table_008', venueId: 'venue_001', tableNumber: '8', name: 'Table 8' },
  { id: 'table_009', venueId: 'venue_001', tableNumber: '9', name: 'Table 9' },
  { id: 'table_010', venueId: 'venue_001', tableNumber: '10', name: 'Table 10' },
  { id: 'table_011', venueId: 'venue_001', tableNumber: '11', name: 'Table 11' },
  { id: 'table_012', venueId: 'venue_001', tableNumber: '12', name: 'Table 12' }
];

const products = [
  { id: 'product_001', venueId: 'venue_001', name: 'Margherita Pizza', description: 'Classic pizza with tomato sauce, mozzarella, and basil', price: 12.99, currency: 'RON', isModifier: false, isActive: true },
  { id: 'product_002', venueId: 'venue_001', name: 'Pepperoni Pizza', description: 'Pizza with tomato sauce, mozzarella, and pepperoni', price: 15.99, currency: 'RON', isModifier: false, isActive: true },
  { id: 'product_003', venueId: 'venue_001', name: 'Vegetarian Pizza', description: 'Pizza with vegetables and cheese', price: 16.99, currency: 'RON', isModifier: false, isActive: true },
  { id: 'product_004', venueId: 'venue_001', name: 'Chicken Alfredo', description: 'Creamy pasta with grilled chicken', price: 18.99, currency: 'RON', isModifier: false, isActive: true },
  { id: 'product_005', venueId: 'venue_001', name: 'Caesar Salad', description: 'Fresh romaine lettuce with Caesar dressing', price: 8.99, currency: 'RON', isModifier: false, isActive: true },
  { id: 'product_006', venueId: 'venue_001', name: 'Grilled Salmon', description: 'Fresh Atlantic salmon grilled to perfection', price: 24.99, currency: 'RON', isModifier: false, isActive: true },
  { id: 'product_007', venueId: 'venue_001', name: 'Beef Burger', description: 'Juicy beef burger with lettuce, tomato, and cheese', price: 16.99, currency: 'RON', isModifier: false, isActive: true },
  { id: 'product_008', venueId: 'venue_001', name: 'Fish and Chips', description: 'Beer-battered fish with crispy fries', price: 19.99, currency: 'RON', isModifier: false, isActive: true },
  { id: 'product_009', venueId: 'venue_001', name: 'Garlic Bread', description: 'Toasted bread with garlic butter', price: 4.99, currency: 'RON', isModifier: false, isActive: true },
  { id: 'product_010', venueId: 'venue_001', name: 'Tiramisu', description: 'Classic Italian dessert', price: 7.99, currency: 'RON', isModifier: false, isActive: true },
  // Modifiers
  { id: 'modifier_001', venueId: 'venue_001', name: 'Extra Cheese', description: 'Additional cheese', price: 1.50, currency: 'RON', isModifier: true, isActive: true },
  { id: 'modifier_002', venueId: 'venue_001', name: 'Extra Pepperoni', description: 'Additional pepperoni', price: 2.00, currency: 'RON', isModifier: true, isActive: true },
  { id: 'modifier_003', venueId: 'venue_001', name: 'No Onions', description: 'Remove onions', price: 0, currency: 'RON', isModifier: true, isActive: true },
  { id: 'modifier_004', venueId: 'venue_001', name: 'Extra Sauce', description: 'Additional sauce', price: 0.50, currency: 'RON', isModifier: true, isActive: true },
  { id: 'modifier_005', venueId: 'venue_001', name: 'Gluten Free Base', description: 'Gluten-free pizza base', price: 3.00, currency: 'RON', isModifier: true, isActive: true }
];

export async function populateFirebaseData() {
  try {
    console.log('Starting Firebase data population...');

    // Add venues
    for (const venue of venues) {
      await setDoc(doc(db, 'venues', venue.id), venue);
      console.log(`✅ Added venue: ${venue.name}`);
    }

    // Add waiters
    for (const waiter of waiters) {
      await setDoc(doc(db, 'waiters', waiter.id), waiter);
      console.log(`✅ Added waiter: ${waiter.name.first} ${waiter.name.last}`);
    }

    // Add tables
    for (const table of tables) {
      await setDoc(doc(db, 'tables', table.id), table);
      console.log(`✅ Added table: ${table.name}`);
    }

    // Add products
    for (const product of products) {
      await setDoc(doc(db, 'products', product.id), product);
      console.log(`✅ Added product: ${product.name}`);
    }

    console.log('📦 Basic data populated successfully!');
    console.log('🍽️ Now adding sample orders...');

    // Add sample orders with items
    await addSampleOrders();

    console.log('Firebase data population completed successfully!');
  } catch (error) {
    console.error('Error populating Firebase data:', error);
    throw error;
  }
}

async function addSampleOrders() {
  const orders = [
    {
      id: 'order_001',
      venueId: 'venue_001',
      tableId: 'table_012',
      tableName: 'Table 12',
      orderNumber: 'ORD-001',
      status: 'pending',
      totals: {
        subtotal: 19.48,
        tips: 0,
        discount: 0,
        total: 19.48,
        currency: 'RON'
      },
      specialInstructions: 'Extra crispy crust',
      createdAt: Timestamp.fromDate(new Date(Date.now() - 5 * 60000)), // 5 minutes ago
      updatedAt: Timestamp.fromDate(new Date(Date.now() - 5 * 60000))
    },
    {
      id: 'order_002',
      venueId: 'venue_001',
      tableId: 'table_008',
      tableName: 'Table 8',
      orderNumber: 'ORD-002',
      status: 'pending',
      totals: {
        subtotal: 20.99,
        tips: 0,
        discount: 0,
        total: 20.99,
        currency: 'RON'
      },
      specialInstructions: 'No onions please',
      createdAt: Timestamp.fromDate(new Date(Date.now() - 12 * 60000)), // 12 minutes ago
      updatedAt: Timestamp.fromDate(new Date(Date.now() - 12 * 60000))
    },
    {
      id: 'order_003',
      venueId: 'venue_001',
      tableId: 'table_005',
      tableName: 'Table 5',
      orderNumber: 'ORD-003',
      status: 'pending',
      totals: {
        subtotal: 33.98,
        tips: 0,
        discount: 0,
        total: 33.98,
        currency: 'RON'
      },
      createdAt: Timestamp.fromDate(new Date(Date.now() - 8 * 60000)), // 8 minutes ago
      updatedAt: Timestamp.fromDate(new Date(Date.now() - 8 * 60000))
    },
    {
      id: 'order_004',
      venueId: 'venue_001',
      tableId: 'table_003',
      tableName: 'Table 3',
      orderNumber: 'ORD-004',
      status: 'confirmed',
      totals: {
        subtotal: 33.98,
        tips: 0,
        discount: 0,
        total: 33.98,
        currency: 'RON'
      },
      specialInstructions: 'Medium rare',
      createdAt: Timestamp.fromDate(new Date(Date.now() - 60 * 60000)), // 1 hour ago
      updatedAt: Timestamp.fromDate(new Date(Date.now() - 45 * 60000))  // 45 minutes ago
    },
    {
      id: 'order_005',
      venueId: 'venue_001',
      tableId: 'table_007',
      tableName: 'Table 7',
      orderNumber: 'ORD-005',
      status: 'rejected',
      totals: {
        subtotal: 16.99,
        tips: 0,
        discount: 0,
        total: 16.99,
        currency: 'RON'
      },
      rejectionReason: 'Out of stock',
      rejectionNote: 'No more vegetarian toppings available',
      createdAt: Timestamp.fromDate(new Date(Date.now() - 90 * 60000)), // 1.5 hours ago
      updatedAt: Timestamp.fromDate(new Date(Date.now() - 85 * 60000))  // 1 hour 25 minutes ago
    },
    {
      id: 'order_006',
      venueId: 'venue_001',
      tableId: 'table_002',
      tableName: 'Table 2',
      orderNumber: 'ORD-006',
      status: 'confirmed',
      totals: {
        subtotal: 27.98,
        tips: 0,
        discount: 0,
        total: 27.98,
        currency: 'RON'
      },
      createdAt: Timestamp.fromDate(new Date(Date.now() - 120 * 60000)), // 2 hours ago
      updatedAt: Timestamp.fromDate(new Date(Date.now() - 115 * 60000))  // 1 hour 55 minutes ago
    }
  ];

  // Order items for each order
  const orderItems = {
    'order_001': [
      {
        id: 'item_001',
        productId: 'product_001',
        productName: 'Margherita Pizza',
        quantity: 1,
        unitPrice: 12.99,
        totalPrice: 12.99,
        currency: 'RON',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        id: 'item_002',
        productId: 'modifier_001',
        productName: 'Extra Cheese',
        quantity: 1,
        unitPrice: 1.50,
        totalPrice: 1.50,
        currency: 'RON',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        id: 'item_003',
        productId: 'product_009',
        productName: 'Garlic Bread',
        quantity: 1,
        unitPrice: 4.99,
        totalPrice: 4.99,
        currency: 'RON',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ],
    'order_002': [
      {
        id: 'item_004',
        productId: 'product_004',
        productName: 'Chicken Alfredo',
        quantity: 1,
        unitPrice: 18.99,
        totalPrice: 18.99,
        currency: 'RON',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        id: 'item_005',
        productId: 'modifier_003',
        productName: 'No Onions',
        quantity: 1,
        unitPrice: 0,
        totalPrice: 0,
        currency: 'RON',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        id: 'item_006',
        productId: 'modifier_001',
        productName: 'Extra Cheese',
        quantity: 1,
        unitPrice: 2.00,
        totalPrice: 2.00,
        currency: 'RON',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ],
    'order_003': [
      {
        id: 'item_007',
        productId: 'product_005',
        productName: 'Caesar Salad',
        quantity: 1,
        unitPrice: 8.99,
        totalPrice: 8.99,
        currency: 'RON',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        id: 'item_008',
        productId: 'product_006',
        productName: 'Grilled Salmon',
        quantity: 1,
        unitPrice: 24.99,
        totalPrice: 24.99,
        currency: 'RON',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ],
    'order_004': [
      {
        id: 'item_009',
        productId: 'product_007',
        productName: 'Beef Burger',
        quantity: 2,
        unitPrice: 16.99,
        totalPrice: 33.98,
        currency: 'RON',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ],
    'order_005': [
      {
        id: 'item_010',
        productId: 'product_003',
        productName: 'Vegetarian Pizza',
        quantity: 1,
        unitPrice: 16.99,
        totalPrice: 16.99,
        currency: 'RON',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ],
    'order_006': [
      {
        id: 'item_011',
        productId: 'product_008',
        productName: 'Fish and Chips',
        quantity: 1,
        unitPrice: 19.99,
        totalPrice: 19.99,
        currency: 'RON',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      },
      {
        id: 'item_012',
        productId: 'product_010',
        productName: 'Tiramisu',
        quantity: 1,
        unitPrice: 7.99,
        totalPrice: 7.99,
        currency: 'RON',
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      }
    ]
  };

  // Add orders and their items
  for (const order of orders) {
    await setDoc(doc(db, 'orders', order.id), order);
    console.log(`✅ Added order: ${order.orderNumber}`);

    // Add order items as subcollection
    const items = orderItems[order.id as keyof typeof orderItems];
    if (items) {
      for (const item of items) {
        await setDoc(doc(db, 'orders', order.id, 'order_items', item.id), item);
        console.log(`  ➕ Added item: ${item.productName}`);
      }
    }
  }
}

// Main execution
async function main() {
  console.log('🔥 Firebase Data Population Script');
  console.log('=====================================');
  console.log(`📡 Using Firebase project: ${environment.firebase.projectId}`);
  console.log('');

  try {
    await populateFirebaseData();
    console.log('');
    console.log('🎉 Success! Firebase data population completed.');
    console.log('🔑 You can now login with: <EMAIL>');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    console.error('💡 Make sure your Firebase project is accessible and you have write permissions.');
    process.exit(1);
  }
}

// Run the script
main();
