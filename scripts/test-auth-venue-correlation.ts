import { initializeApp } from 'firebase/app';
import { getFirestore, doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';

// Firebase config (replace with your actual config)
const firebaseConfig = {
  apiKey: "AIzaSyBCAxpWlrKWKu9qyzGkXSVLm_uwmXP3HCU",
  authDomain: "orderflowqr.firebaseapp.com",
  projectId: "orderflowqr",
  storageBucket: "orderflowqr.firebasestorage.app",
  messagingSenderId: "338803973128",
  appId: "1:338803973128:web:3220d218233f03826445bd"
};

async function testAuthVenueCorrelation() {
  console.log('🔍 Testing Auth-Venue Correlation...\n');

  // Initialize Firebase
  const app = initializeApp(firebaseConfig);
  const db = getFirestore(app);

  try {
    // Test 1: Check if waiter exists for admin email
    console.log('1. Testing waiter lookup by email...');
    const testEmail = '<EMAIL>';
    
    const waitersRef = collection(db, 'waiters');
    const q = query(waitersRef, where('email', '==', testEmail));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      console.log('❌ No waiter found for email:', testEmail);
      return;
    }

    const waiterDoc = querySnapshot.docs[0];
    const waiterData = { id: waiterDoc.id, ...waiterDoc.data() };
    console.log('✅ Waiter found:', waiterData.name.first, waiterData.name.last);
    console.log('   Role:', waiterData.role);
    console.log('   Venue ID:', waiterData.venueId);

    // Test 2: Check if venue exists
    console.log('\n2. Testing venue lookup...');
    const venueDoc = doc(db, 'venues', waiterData.venueId);
    const venueSnapshot = await getDoc(venueDoc);

    if (!venueSnapshot.exists()) {
      console.log('❌ No venue found for ID:', waiterData.venueId);
      return;
    }

    const venueData = { id: venueSnapshot.id, ...venueSnapshot.data() };
    console.log('✅ Venue found:', venueData.name);

    // Test 3: Check orders for this venue
    console.log('\n3. Testing orders filtering by venue...');
    const ordersRef = collection(db, 'orders');
    const ordersQuery = query(ordersRef, where('venueId', '==', venueData.id));
    const ordersSnapshot = await getDocs(ordersQuery);

    console.log('✅ Orders found for venue:', ordersSnapshot.size);
    ordersSnapshot.docs.forEach(orderDoc => {
      const orderData = orderDoc.data();
      console.log(`   - Order ${orderData.orderNumber}: ${orderData.status} (${orderData.tableName})`);
    });

    console.log('\n🎉 Auth-Venue correlation test completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   Email: ${testEmail}`);
    console.log(`   Waiter: ${waiterData.name.first} ${waiterData.name.last} (${waiterData.role})`);
    console.log(`   Venue: ${venueData.name}`);
    console.log(`   Orders: ${ordersSnapshot.size} found`);

  } catch (error) {
    console.error('❌ Error testing correlation:', error);
  }
}

// Run the test
testAuthVenueCorrelation();
