#!/bin/bash

echo "🔐 Deploying Firestore Security Rules..."
echo "========================================"

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed."
    echo "📦 Install it with: npm install -g firebase-tools"
    echo "🔐 Then login with: firebase login"
    exit 1
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "❌ You are not logged in to Firebase."
    echo "🔐 Please run: firebase login"
    exit 1
fi

echo "📋 Deploying security rules from firestore.rules..."

# Deploy the security rules
firebase deploy --only firestore:rules

if [ $? -eq 0 ]; then
    echo "✅ Firestore security rules deployed successfully!"
    echo ""
    echo "🔍 You can view your rules at:"
    echo "https://console.firebase.google.com/project/orderflowqr/firestore/rules"
    echo ""
    echo "🔑 Key changes made:"
    echo "  - Waiters collection: Allow read access for email lookup"
    echo "  - Simplified permissions for development"
    echo "  - Maintained security for write operations"
else
    echo "❌ Failed to deploy security rules."
    echo "💡 Make sure you have the correct permissions and project setup."
fi
