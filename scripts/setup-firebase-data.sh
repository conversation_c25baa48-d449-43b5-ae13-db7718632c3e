#!/bin/bash

# Firebase Data Setup Script
# This script temporarily relaxes Firestore rules, populates data, then restores secure rules

echo "🔥 Firebase Data Setup Script"
echo "=============================="
echo ""

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed."
    echo "💡 Install it with: npm install -g firebase-tools"
    echo "💡 Then login with: firebase login"
    exit 1
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "❌ You are not logged in to Firebase."
    echo "💡 Login with: firebase login"
    exit 1
fi

echo "📋 Step 1: Deploying temporary Firestore rules (allows unauthenticated writes)..."
firebase deploy --only firestore:rules --project orderflowqr --config firestore.rules.temp

if [ $? -ne 0 ]; then
    echo "❌ Failed to deploy temporary rules"
    exit 1
fi

echo "✅ Temporary rules deployed"
echo ""

echo "📦 Step 2: Populating Firebase data..."
npm run populate-firebase

if [ $? -ne 0 ]; then
    echo "❌ Failed to populate data"
    echo "🔄 Restoring original rules..."
    firebase deploy --only firestore:rules --project orderflowqr
    exit 1
fi

echo "✅ Data populated successfully"
echo ""

echo "🔒 Step 3: Restoring secure Firestore rules..."
firebase deploy --only firestore:rules --project orderflowqr

if [ $? -ne 0 ]; then
    echo "❌ Failed to restore original rules"
    echo "⚠️  WARNING: Your Firestore is still using temporary rules!"
    echo "💡 Manually deploy rules with: firebase deploy --only firestore:rules"
    exit 1
fi

echo "✅ Secure rules restored"
echo ""
echo "🎉 Firebase setup completed successfully!"
echo "🔑 You can now login to the app with: <EMAIL>"
echo ""
echo "📱 Next steps:"
echo "1. Start the app: npm start"
echo "2. <NAME_EMAIL> / admin123"
echo "3. Test the order management features"
