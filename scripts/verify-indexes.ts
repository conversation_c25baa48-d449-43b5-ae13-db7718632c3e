const { initializeApp } = require('firebase/app');
const { getFirestore, collection, query, where, orderBy, getDocs } = require('firebase/firestore');

// Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyBCAxpWlrKWKu9qyzGkXSVLm_uwmXP3HCU",
  authDomain: "orderflowqr.firebaseapp.com",
  projectId: "orderflowqr",
  storageBucket: "orderflowqr.firebasestorage.app",
  messagingSenderId: "338803973128",
  appId: "1:338803973128:web:3220d218233f03826445bd"
};

async function verifyIndexes() {
  console.log('🔍 Verifying Firestore Indexes...');
  console.log('==================================\n');

  // Initialize Firebase
  const app = initializeApp(firebaseConfig);
  const db = getFirestore(app);

  const tests = [
    {
      name: 'Waiters by Email (Critical for Auth)',
      test: async () => {
        const waitersRef = collection(db, 'waiters');
        const q = query(waitersRef, where('email', '==', '<EMAIL>'));
        const start = Date.now();
        const snapshot = await getDocs(q);
        const duration = Date.now() - start;
        return { success: true, duration, count: snapshot.size };
      }
    },
    {
      name: 'Orders by Venue + CreatedAt',
      test: async () => {
        const ordersRef = collection(db, 'orders');
        const q = query(
          ordersRef, 
          where('venueId', '==', 'venue_001'),
          orderBy('createdAt', 'desc')
        );
        const start = Date.now();
        const snapshot = await getDocs(q);
        const duration = Date.now() - start;
        return { success: true, duration, count: snapshot.size };
      }
    },
    {
      name: 'Orders by Venue + Status + CreatedAt',
      test: async () => {
        const ordersRef = collection(db, 'orders');
        const q = query(
          ordersRef,
          where('venueId', '==', 'venue_001'),
          where('status', '==', 'pending'),
          orderBy('createdAt', 'desc')
        );
        const start = Date.now();
        const snapshot = await getDocs(q);
        const duration = Date.now() - start;
        return { success: true, duration, count: snapshot.size };
      }
    },
    {
      name: 'Products by Venue + IsActive',
      test: async () => {
        const productsRef = collection(db, 'products');
        const q = query(
          productsRef,
          where('venueId', '==', 'venue_001'),
          where('isActive', '==', true)
        );
        const start = Date.now();
        const snapshot = await getDocs(q);
        const duration = Date.now() - start;
        return { success: true, duration, count: snapshot.size };
      }
    },
    {
      name: 'Tables by Venue',
      test: async () => {
        const tablesRef = collection(db, 'tables');
        const q = query(tablesRef, where('venueId', '==', 'venue_001'));
        const start = Date.now();
        const snapshot = await getDocs(q);
        const duration = Date.now() - start;
        return { success: true, duration, count: snapshot.size };
      }
    }
  ];

  let allPassed = true;
  let totalDuration = 0;

  for (const test of tests) {
    try {
      console.log(`🧪 Testing: ${test.name}`);
      const result = await test.test();
      totalDuration += result.duration;
      
      if (result.duration < 1000) {
        console.log(`   ✅ PASS - ${result.duration}ms (${result.count} documents)`);
      } else {
        console.log(`   ⚠️  SLOW - ${result.duration}ms (${result.count} documents) - Index may be missing`);
        allPassed = false;
      }
    } catch (error: any) {
      console.log(`   ❌ FAIL - ${error.message}`);
      if (error.message.includes('index')) {
        console.log(`   💡 Missing index detected - Create the required composite index`);
      }
      allPassed = false;
    }
    console.log('');
  }

  console.log('📊 SUMMARY');
  console.log('==========');
  console.log(`Total test duration: ${totalDuration}ms`);
  console.log(`Average query time: ${Math.round(totalDuration / tests.length)}ms`);
  
  if (allPassed) {
    console.log('🎉 All indexes are working correctly!');
    console.log('✅ Your app should perform well with these query speeds.');
  } else {
    console.log('⚠️  Some indexes are missing or slow.');
    console.log('📋 Please create the missing indexes using the setup guide.');
    console.log('🔗 See: FIRESTORE_INDEXES_SETUP.md');
  }

  console.log('\n🔗 Useful Links:');
  console.log('Firebase Console: https://console.firebase.google.com/project/orderflowqr/firestore/indexes');
  console.log('Index Setup Guide: ./FIRESTORE_INDEXES_SETUP.md');
}

// Run the verification
verifyIndexes().catch(console.error);
