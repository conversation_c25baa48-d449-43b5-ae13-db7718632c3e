# Firestore Indexes Setup Guide

This guide will help you create the necessary Firestore indexes for optimal performance in your Order Flow QR application.

## 🎯 Why You Need These Indexes

Your app uses complex queries that filter by multiple fields. Without proper indexes, these queries will be slow or fail entirely. The indexes below are specifically designed for your auth-venue correlation system.

## 📋 Required Indexes

### 🏢 Orders Collection (Most Important)

#### Index 1: Main Orders Query
- **Collection:** `orders`
- **Fields:**
  - `venueId` (Ascending)
  - `createdAt` (Descending)
- **Purpose:** Load all orders for a venue, sorted by newest first

#### Index 2: Orders by Status
- **Collection:** `orders`
- **Fields:**
  - `venueId` (Ascending)
  - `status` (Ascending)
  - `createdAt` (Descending)
- **Purpose:** Filter pending/confirmed/rejected orders by venue

#### Index 3: Orders by Table
- **Collection:** `orders`
- **Fields:**
  - `venueId` (Ascending)
  - `tableId` (Ascending)
  - `createdAt` (Descending)
- **Purpose:** View orders for specific tables

#### Index 4: Orders by Waiter
- **Collection:** `orders`
- **Fields:**
  - `venueId` (Ascending)
  - `assignedWaiter` (Ascending)
  - `status` (Ascending)
- **Purpose:** Filter orders assigned to specific waiters

### 👥 Waiters Collection

#### Index 5: Email Lookup (Critical)
- **Collection:** `waiters`
- **Fields:**
  - `email` (Ascending)
- **Purpose:** User authentication - find waiter by email

#### Index 6: Waiters by Role
- **Collection:** `waiters`
- **Fields:**
  - `venueId` (Ascending)
  - `role` (Ascending)
- **Purpose:** Filter waiters by venue and role

### 🍽️ Products Collection

#### Index 7: Active Products
- **Collection:** `products`
- **Fields:**
  - `venueId` (Ascending)
  - `isActive` (Ascending)
- **Purpose:** Load active menu items for a venue

#### Index 8: Products vs Modifiers
- **Collection:** `products`
- **Fields:**
  - `venueId` (Ascending)
  - `isModifier` (Ascending)
  - `isActive` (Ascending)
- **Purpose:** Separate products from modifiers

### 🪑 Tables Collection

#### Index 9: Tables by Venue
- **Collection:** `tables`
- **Fields:**
  - `venueId` (Ascending)
- **Purpose:** Load all tables for a venue

## 🛠️ How to Create Indexes

### Method 1: Firebase Console (Recommended)

1. **Go to Firebase Console**
   - Visit: https://console.firebase.google.com
   - Select your project: `orderflowqr`

2. **Navigate to Firestore**
   - Click "Firestore Database" in the left sidebar
   - Click "Indexes" tab

3. **Create Each Index**
   - Click "Create Index"
   - Enter the collection name
   - Add fields in the exact order specified
   - Set ascending/descending as noted
   - Click "Create"

### Method 2: Firebase CLI (Advanced)

1. **Install Firebase CLI**
   ```bash
   npm install -g firebase-tools
   firebase login
   ```

2. **Deploy Indexes**
   ```bash
   chmod +x scripts/deploy-indexes.sh
   ./scripts/deploy-indexes.sh
   ```

### Method 3: Automatic Creation

1. **Run Your App**
   - Start your development server
   - Login and use the app normally

2. **Check Console**
   - Open browser developer tools
   - Look for Firestore error messages
   - Click the provided index creation links

## ⚡ Priority Order

Create these indexes first (most critical):

1. ✅ **waiters: email** - Required for login
2. ✅ **orders: venueId + createdAt** - Main orders query
3. ✅ **orders: venueId + status + createdAt** - Filter by status
4. ✅ **products: venueId + isActive** - Menu loading
5. ✅ **tables: venueId** - Table management

## 🔍 Verification

After creating indexes, verify they're working:

1. **Check Index Status**
   - Go to Firebase Console > Firestore > Indexes
   - Ensure all indexes show "Enabled" status

2. **Test Your App**
   - Login with `<EMAIL>`
   - Check that orders load quickly
   - Verify no console errors

3. **Monitor Performance**
   - Use Firebase Performance Monitoring
   - Check query execution times

## 🚨 Common Issues

### Index Creation Failed
- **Cause:** Insufficient permissions
- **Solution:** Ensure you're a project owner/editor

### Queries Still Slow
- **Cause:** Missing composite index
- **Solution:** Check console for specific index requirements

### Authentication Fails
- **Cause:** Missing email index on waiters collection
- **Solution:** Create the email index first

## 📊 Expected Performance

With proper indexes:
- **Login:** < 500ms
- **Orders loading:** < 1s
- **Status filtering:** < 300ms
- **Real-time updates:** Instant

## 🎉 Completion

Once all indexes are created:
1. Your app will perform significantly better
2. All queries will execute quickly
3. Real-time updates will be instant
4. The system can scale to thousands of orders

**Total indexes needed: 9**
**Estimated setup time: 10-15 minutes**
**Index build time: 5-10 minutes (automatic)**
