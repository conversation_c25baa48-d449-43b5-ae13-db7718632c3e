# Fix Firestore Security Rules for Auth-Venue Correlation

## 🚨 Problem
You're getting this error: `Missing or insufficient permissions` when trying to load waiter profiles.

**Root Cause:** The current security rules don't allow querying the `waiters` collection by email, which is required for the auth-venue correlation system.

## 🔧 Solution

### Method 1: Firebase Console (Recommended)

1. **Go to Firebase Console**
   - Visit: https://console.firebase.google.com/project/orderflowqr/firestore/rules

2. **Replace the current rules** with this updated version:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Venues - Only authenticated users can read
    match /venues/{venueId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }

    // Waiters - Allow authenticated users to query by email and read their own data
    match /waiters/{waiterId} {
      // Allow reading any waiter document for authenticated users
      // This is needed for the email lookup during authentication
      allow read: if request.auth != null;
      
      // Allow writing only to own document or by admins/managers
      allow write: if request.auth != null && (
        // User can update their own profile
        request.auth.uid == waiterId ||
        // Or user is admin/manager (check after they have a profile)
        (exists(/databases/$(database)/documents/waiters/$(request.auth.uid)) &&
         get(/databases/$(database)/documents/waiters/$(request.auth.uid)).data.role in ['admin', 'manager'])
      );
    }

    // Tables - Authenticated users can read, admins/managers can write
    match /tables/{tableId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }

    // Products - Authenticated users can read, admins/managers can write
    match /products/{productId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }

    // Orders - Authenticated users can read and write
    match /orders/{orderId} {
      allow read, write: if request.auth != null;
    }

    // Order Items (subcollection) - Authenticated users can read and write
    match /orders/{orderId}/order_items/{orderItemId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

3. **Click "Publish"** to deploy the new rules

### Method 2: Firebase CLI (Advanced)

1. **Install Firebase CLI** (if not already installed):
   ```bash
   npm install -g firebase-tools
   firebase login
   ```

2. **Deploy the rules**:
   ```bash
   chmod +x scripts/deploy-security-rules.sh
   ./scripts/deploy-security-rules.sh
   ```

## 🔑 Key Changes Made

### **Before (Problematic):**
```javascript
// Waiters - Only authenticated users can read their own data
match /waiters/{waiterId} {
  allow read, write: if request.auth != null &&
    (request.auth.uid == waiterId ||
     get(/databases/$(database)/documents/waiters/$(request.auth.uid)).data.role in ['admin', 'manager']);
}
```

### **After (Fixed):**
```javascript
// Waiters - Allow authenticated users to query by email and read their own data
match /waiters/{waiterId} {
  // Allow reading any waiter document for authenticated users
  // This is needed for the email lookup during authentication
  allow read: if request.auth != null;
  
  // Allow writing only to own document or by admins/managers
  allow write: if request.auth != null && (
    // User can update their own profile
    request.auth.uid == waiterId ||
    // Or user is admin/manager (check after they have a profile)
    (exists(/databases/$(database)/documents/waiters/$(request.auth.uid)) &&
     get(/databases/$(database)/documents/waiters/$(request.auth.uid)).data.role in ['admin', 'manager'])
  );
}
```

## 🎯 Why This Fixes the Issue

1. **Email Lookup Allowed**: Now authenticated users can read waiter documents to find profiles by email
2. **Security Maintained**: Write access is still restricted to appropriate users
3. **Auth Flow Works**: The UserService can now query `waiters` collection by email during login

## 🧪 Test the Fix

After updating the rules, test the auth flow:

1. **Login** with `<EMAIL>`
2. **Check Console** - The error should be gone
3. **Verify Venue Header** - Should show "Bella Vista Restaurant"
4. **Check Orders** - Should load orders for the venue

## 🔒 Security Considerations

**This is a development-friendly setup.** For production, you may want to:

1. **Restrict read access** to waiters from the same venue only
2. **Add more granular permissions** based on roles
3. **Implement field-level security** for sensitive data

## 🚨 Alternative Quick Fix (Temporary)

If you need a quick temporary fix for development, you can use this simple rule:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow all authenticated users full access (DEVELOPMENT ONLY)
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

**⚠️ Warning:** Only use this for development. Never use this in production!

## ✅ Expected Result

After applying these rules:
- ✅ Login works without permission errors
- ✅ Venue header displays correctly
- ✅ Orders load filtered by venue
- ✅ Auth-venue correlation functions properly
