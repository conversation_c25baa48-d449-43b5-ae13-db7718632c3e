rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Venues - Only authenticated users can read
    match /venues/{venueId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }

    // Waiters - Allow authenticated users to query by email and read their own data
    match /waiters/{waiterId} {
      // Allow reading any waiter document for authenticated users
      // This is needed for the email lookup during authentication
      allow read: if request.auth != null;
      
      // Allow writing only to own document or by admins/managers
      allow write: if request.auth != null && (
        // User can update their own profile
        request.auth.uid == waiterId ||
        // Or user is admin/manager (check after they have a profile)
        (exists(/databases/$(database)/documents/waiters/$(request.auth.uid)) &&
         get(/databases/$(database)/documents/waiters/$(request.auth.uid)).data.role in ['admin', 'manager'])
      );
    }

    // Tables - Authenticated users can read, admins/managers can write
    match /tables/{tableId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }

    // Products - Authenticated users can read, admins/managers can write
    match /products/{productId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }

    // Orders - Authenticated users can read and write
    match /orders/{orderId} {
      allow read, write: if request.auth != null;
    }

    // Order Items (subcollection) - Authenticated users can read and write
    match /orders/{orderId}/order_items/{orderItemId} {
      allow read, write: if request.auth != null;
    }
  }
}
